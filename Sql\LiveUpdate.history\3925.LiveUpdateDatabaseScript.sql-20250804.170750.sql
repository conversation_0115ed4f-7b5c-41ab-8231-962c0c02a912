﻿-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- BEGIN LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
SET NOCOUNT ON
SET NOEXEC OFF
SET ARITHABORT ON
SET XACT_ABORT ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO
BEGIN TRAN
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- TRANSFER SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 1: Drop Unique Constraints U_SlamcoreAuthenticationDetailsSlamcoreDevice 
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'U_SlamcoreAuthenticationDetailsSlamcoreDevice' AND object_id = OBJECT_ID('[dbo].[SlamcoreAuthenticationDetails]'))
BEGIN
DROP INDEX U_SlamcoreAuthenticationDetailsSlamcoreDevice 
	ON [dbo].[SlamcoreAuthenticationDetails]
END
GO
IF (OBJECT_ID(N'[dbo].[U_SlamcoreAuthenticationDetailsSlamcoreDevice]', 'UQ') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAuthenticationDetails] DROP CONSTRAINT [U_SlamcoreAuthenticationDetailsSlamcoreDevice]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 1, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 1' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 2: Drop Foreign Key constraint dbo.SlamcoreAuthenticationDetails 
IF (OBJECT_ID(N'[dbo].[FK_SlamcoreAuthenticationDetails_SlamcoreDevice_cb92a708-8830-490f-875a-f8261bce90f1]', 'F') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAuthenticationDetails] DROP CONSTRAINT [FK_SlamcoreAuthenticationDetails_SlamcoreDevice_cb92a708-8830-490f-875a-f8261bce90f1]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 2, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 2' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- RENAME TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (soft)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 3: Soft drop (Rename out of the way) column dbo.SlamcoreAuthenticationDetails.SlamcoreDeviceId 
EXEC sp_rename '[dbo].[SlamcoreAuthenticationDetails].[SlamcoreDeviceId]', 'SlamcoreDeviceId_$@GO.dropped._$@GO', 'COLUMN'
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 3, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 3' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN RENAMEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN ADDs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 4: add column dbo.SlamcoreDevice.SlamcoreAuthenticationDetailsId 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD [SlamcoreAuthenticationDetailsId] [uniqueidentifier] NULL 
GO

IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 4, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 4' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN MODIFYs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (hard)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 5: Drop column dbo.SlamcoreAuthenticationDetails.SlamcoreDeviceId_$@GO.dropped._$@GO 
-- first drop any default value constraint
IF (OBJECT_ID(N'[dbo].[DF_field_id_3b861cba-dd23-451a-b90f-7885b12f72c2]', 'D') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAuthenticationDetails] DROP CONSTRAINT [DF_field_id_3b861cba-dd23-451a-b90f-7885b12f72c2]
END
GO
-- drop the column
ALTER TABLE [dbo].[SlamcoreAuthenticationDetails] DROP COLUMN [SlamcoreDeviceId_$@GO.dropped._$@GO]
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 5, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 5' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 6: Add Foreign Key Constraint dbo.SlamcoreDevice to dbo.SlamcoreAuthenticationDetails 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD CONSTRAINT [FK_SlamcoreDevice_SlamcoreAuthenticationDetails_7b8d8d19-9ca4-42d7-9df8-2339518bf0c3] FOREIGN KEY
	(
		[SlamcoreAuthenticationDetailsId] 
	)
	REFERENCES [dbo].[SlamcoreAuthenticationDetails]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 6, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 6' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 7: Add/Update Unique constraint U_SlamcoreDeviceSlamcoreAuthenticationDetails 
CREATE UNIQUE NONCLUSTERED INDEX [U_SlamcoreDeviceSlamcoreAuthenticationDetails]
ON [dbo].[SlamcoreDevice] 	
	([SlamcoreAuthenticationDetailsId]) 
WHERE 
	[SlamcoreAuthenticationDetailsId] IS NOT NULL  
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 7, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 7' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MODEL TO DATABASE SYNCHRONISATION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
UPDATE [GO.LiveUpdate].[ModelSync] SET [ModelRevisionId] = 3925, [When] = GETUTCDATE() WHERE Id = 'AF3DF4FF-A05A-4969-9796-FAC22A6ED2AF'
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COMMIT LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
IF @@TRANCOUNT > 0 
BEGIN 
	COMMIT TRAN PRINT 'Synchronization completed successfully.' 
END
GO
SET NOEXEC OFF
GO
