sequenceDiagram
    participant User as 👤 User
    participant Browser as 🌐 Browser
    participant App as 📱 Application.html
    participant AppCtrl as 🎮 ApplicationController
    participant CustomCtrl as 🔧 CustomController
    participant ViewLoader as 📄 ViewLoader
    participant SourceHandler as 📦 SourceHandler
    participant DataSet as 💾 ObjectsDataSet
    participant Proxy as 🔌 ComponentProxy
    participant API as 🌍 Backend API
    participant Auth as 🔐 Authentication
    participant Dashboard as 📊 Dashboard Components
    participant Vehicle as 🚗 Vehicle Management
    participant Driver as 👨‍💼 Driver Management
    participant Reports as 📈 Reporting Components
    participant UI as 🎨 UI Elements

    Note over User,UI: XQ360 Fleet Management System - Frontend Component Hierarchy & Interaction Flow

    %% Application Initialization
    User->>Browser: Access XQ360 Application
    Browser->>App: Load Application.html
    App->>AppCtrl: Initialize ApplicationController
    AppCtrl->>CustomCtrl: Initialize CustomController
    AppCtrl->>SourceHandler: Initialize SourceHandler
    AppCtrl->>DataSet: Initialize ObjectsDataSet
    AppCtrl->>ViewLoader: Initialize ViewLoader

    Note over AppCtrl: Component Initialization Complete

    %% Authentication Flow
    User->>AppCtrl: Login Request
    AppCtrl->>Auth: Validate Credentials
    Auth->>API: Authenticate User
    API-->>Auth: Authentication Result
    Auth-->>AppCtrl: Authentication Status
    AppCtrl->>Browser: Redirect to Dashboard

    %% Dashboard Loading
    User->>AppCtrl: Navigate to Dashboard
    AppCtrl->>ViewLoader: Load Dashboard View
    ViewLoader->>SourceHandler: Request Required Sources
    SourceHandler->>AppCtrl: Load Component Dependencies
    
    Note over SourceHandler: Loading: DashboardFilterProxy, VehicleDataSet, PersonDataSet

    AppCtrl->>Dashboard: Initialize Dashboard Components
    Dashboard->>Proxy: Request Dashboard Data
    Proxy->>API: Fetch Dashboard KPIs
    API-->>Proxy: Dashboard Data Response
    Proxy-->>Dashboard: Process Dashboard Data
    Dashboard->>UI: Render Dashboard UI
    UI-->>User: Display Dashboard

    %% Vehicle Management Flow
    User->>Dashboard: Click Vehicle Management
    Dashboard->>AppCtrl: Navigate to Vehicle Section
    AppCtrl->>ViewLoader: Load Vehicle Management View
    ViewLoader->>SourceHandler: Request Vehicle Sources
    
    Note over SourceHandler: Loading: VehicleDataSet, VehicleProxy, ModelDataSet

    AppCtrl->>Vehicle: Initialize Vehicle Components
    Vehicle->>Proxy: Request Vehicle List
    Proxy->>API: Fetch Vehicle Data
    API-->>Proxy: Vehicle Data Response
    Proxy-->>Vehicle: Process Vehicle Data
    Vehicle->>UI: Render Vehicle Management UI
    UI-->>User: Display Vehicle Management

    %% Vehicle Detail Flow
    User->>Vehicle: Select Specific Vehicle
    Vehicle->>AppCtrl: Navigate to Vehicle Detail
    AppCtrl->>CustomCtrl: customNavigateToVehicleDetail()
    CustomCtrl->>ViewLoader: Load Vehicle Detail View
    ViewLoader->>SourceHandler: Request Vehicle Detail Sources
    
    Note over SourceHandler: Loading: VehicleDataSet, VehicleGPSDataSet, VehicleDiagnosticDataSet

    AppCtrl->>Vehicle: Initialize Vehicle Detail Components
    Vehicle->>Proxy: Request Vehicle Details
    Proxy->>API: Fetch Vehicle Information
    API-->>Proxy: Vehicle Detail Response
    Proxy-->>Vehicle: Process Vehicle Details
    Vehicle->>UI: Render Vehicle Detail UI
    UI-->>User: Display Vehicle Details

    %% Driver Management Flow
    User->>Dashboard: Click Driver Management
    Dashboard->>AppCtrl: Navigate to Driver Section
    AppCtrl->>ViewLoader: Load Driver Management View
    ViewLoader->>SourceHandler: Request Driver Sources
    
    Note over SourceHandler: Loading: PersonDataSet, PersonToVehicleAccessDataSet, DriverAuthenticationAPIProxy

    AppCtrl->>Driver: Initialize Driver Components
    Driver->>Proxy: Request Driver List
    Proxy->>API: Fetch Driver Data
    API-->>Proxy: Driver Data Response
    Proxy-->>Driver: Process Driver Data
    Driver->>UI: Render Driver Management UI
    UI-->>User: Display Driver Management

    %% Driver Detail Flow
    User->>Driver: Select Specific Driver
    Driver->>AppCtrl: Navigate to Driver Detail
    AppCtrl->>CustomCtrl: customNavigateToPersonDetail()
    CustomCtrl->>ViewLoader: Load Driver Detail View
    ViewLoader->>SourceHandler: Request Driver Detail Sources
    
    Note over SourceHandler: Loading: PersonDataSet, PersonAllocationDataSet, VehicleAccessUtilitiesProxy

    AppCtrl->>Driver: Initialize Driver Detail Components
    Driver->>Proxy: Request Driver Details
    Proxy->>API: Fetch Driver Information
    API-->>Proxy: Driver Detail Response
    Proxy-->>Driver: Process Driver Details
    Driver->>UI: Render Driver Detail UI
    UI-->>User: Display Driver Details

    %% Reporting Flow
    User->>Dashboard: Access Reports
    Dashboard->>AppCtrl: Navigate to Reports
    AppCtrl->>ViewLoader: Load Report Components
    ViewLoader->>SourceHandler: Request Report Sources
    
    Note over SourceHandler: Loading: ReportSubscriptionDataSet, ReportTypeDataSet, Various Report Proxies

    AppCtrl->>Reports: Initialize Report Components
    Reports->>Proxy: Request Report Data
    Proxy->>API: Fetch Report Information
    API-->>Proxy: Report Data Response
    Proxy-->>Reports: Process Report Data
    Reports->>UI: Render Report UI
    UI-->>User: Display Reports

    %% Real-time Updates
    loop Real-time Data Updates
        API->>Proxy: Push Real-time Updates
        Proxy->>DataSet: Update DataSet
        DataSet->>Dashboard: Notify UI Components
        Dashboard->>UI: Update Dashboard Display
        UI-->>User: Show Real-time Changes
    end

    %% Component Lifecycle Management
    Note over AppCtrl: Component Lifecycle Management
    AppCtrl->>ViewLoader: Cleanup Unused Views
    ViewLoader->>SourceHandler: Unload Unused Sources
    SourceHandler->>DataSet: Clear Unused Data
    DataSet->>Proxy: Abort Pending Requests

    %% Error Handling
    User->>AppCtrl: Trigger Error Condition
    AppCtrl->>Auth: Check Authentication Status
    Auth->>AppCtrl: Authentication Expired
    AppCtrl->>Browser: Redirect to Login
    Browser-->>User: Show Login Page

    %% Custom UI Elements Integration
    Note over UI: Custom UI Elements Integration
    UI->>UI: Apply ui-elements-linde.css Styling
    UI->>UI: Load Custom Component Styles
    UI->>UI: Apply Responsive Layout
    UI-->>User: Display Customized UI

    Note over User,UI: Frontend Component Hierarchy Complete 