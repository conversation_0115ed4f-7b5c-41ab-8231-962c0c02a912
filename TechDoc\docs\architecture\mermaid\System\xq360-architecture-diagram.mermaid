graph TB
    %% Styling
    classDef presentationLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef businessLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dataLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef integrationLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef external fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef technology fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    %% Presentation Layer
    subgraph PL ["Presentation Layer"]
        WebApp["🌐 Web Application<br/>JavaScript, HTML5, CSS3<br/>Responsive UI"]
        APIGateway["🚪 API Gateway<br/>Centralized Routing<br/>Rate Limiting"]
        AuthService["🔐 Authentication Service<br/>User Auth & Authorization<br/>Multi-tenant Support"]
    end

    %% Business Logic Layer
    subgraph BL ["Business Logic Layer"]
        CoreServices["🏢 Core Services<br/>Fleet Management Logic<br/>Business Rules"]
        WorkflowEngine["🔄 Workflow Engine<br/>Automated Processes<br/>Business Workflows"]
        NotificationService["📢 Notification Service<br/>Real-time Alerts<br/>Communications"]
        VehicleAccessCreation["🚗 Vehicle Access Creation<br/>Access Provisioning<br/>Permission Management"]
        UserAccessQueueService["📋 User Access Queue<br/>Message Queuing<br/>Async Processing"]
        VehicleAccessQueueService["🚛 Vehicle Access Queue<br/>Vehicle Sync<br/>IoT Integration"]
    end

    %% Data Layer
    subgraph DL ["Data Layer"]
        SQLServer["🗄️ SQL Server Database<br/>Primary Data Store<br/>Entity Framework"]
        Redis["⚡ Redis Cache<br/>High-performance Caching<br/>Session Storage"]
        FileStorage["📁 File Storage<br/>Document & Media<br/>Secure Storage"]
        NHibernate["🔧 NHibernate ORM<br/>Data Access Layer<br/>Object Mapping"]
    end

    %% Integration Layer
    subgraph IL ["Integration Layer"]
        ExternalAPIs["🌍 External APIs<br/>Third-party Services<br/>RESTful Integration"]
        MessageQueue["📬 Message Queue<br/>Azure Service Bus<br/>Async Communication"]
        WebhookSupport["🔗 Webhook Support<br/>Event-driven Integration<br/>Real-time Updates"]
        IoTIntegration["📡 IoT Integration<br/>Device Synchronization<br/>Azure IoT Hub"]
    end

    %% External Systems
    subgraph ES ["External Systems"]
        AzureServiceBus["☁️ Azure Service Bus<br/>Message Broker<br/>Reliable Delivery"]
        AzureIoTHub["📡 Azure IoT Hub<br/>Device Management<br/>Telemetry"]
        ThirdPartyAPIs["🔌 Third-party APIs<br/>External Services<br/>Data Exchange"]
        WebhookEndpoints["🎣 Webhook Endpoints<br/>Event Notifications<br/>Real-time Updates"]
    end

    %% Technology Stack
    subgraph TS ["Technology Stack"]
        DotNetCore["⚡ .NET Core<br/>Backend Framework<br/>C# Language"]
        JavaScript["📜 JavaScript<br/>Frontend Framework<br/>Modern Web"]
        IIS["🌐 IIS / Nginx<br/>Web Server<br/>Load Balancing"]
        EntityFramework["🗃️ Entity Framework<br/>Data Access<br/>ORM Framework"]
    end

    %% Data Flow Connections
    WebApp --> APIGateway
    APIGateway --> AuthService
    APIGateway --> CoreServices
    APIGateway --> WorkflowEngine
    APIGateway --> NotificationService

    CoreServices --> VehicleAccessCreation
    CoreServices --> UserAccessQueueService
    CoreServices --> VehicleAccessQueueService

    VehicleAccessCreation --> SQLServer
    UserAccessQueueService --> MessageQueue
    VehicleAccessQueueService --> MessageQueue
    WorkflowEngine --> SQLServer
    NotificationService --> Redis

    MessageQueue --> AzureServiceBus
    IoTIntegration --> AzureIoTHub
    ExternalAPIs --> ThirdPartyAPIs
    WebhookSupport --> WebhookEndpoints

    SQLServer --> NHibernate
    NHibernate --> EntityFramework
    Redis --> DotNetCore
    FileStorage --> IIS

    %% Technology Stack Connections
    DotNetCore --> CoreServices
    JavaScript --> WebApp
    EntityFramework --> SQLServer
    IIS --> WebApp

    %% Apply Styling
    class PL,WebApp,APIGateway,AuthService presentationLayer
    class BL,CoreServices,WorkflowEngine,NotificationService,VehicleAccessCreation,UserAccessQueueService,VehicleAccessQueueService businessLayer
    class DL,SQLServer,Redis,FileStorage,NHibernate dataLayer
    class IL,ExternalAPIs,MessageQueue,WebhookSupport,IoTIntegration integrationLayer
    class ES,AzureServiceBus,AzureIoTHub,ThirdPartyAPIs,WebhookEndpoints external
    class TS,DotNetCore,JavaScript,IIS,EntityFramework technology

    %% Title
    title["🎯 XQ360 Fleet Management System<br/>Layered Architecture Diagram"]

    %% Legend
    subgraph Legend ["Architecture Legend"]
        L1["🎨 Presentation Layer<br/>User Interface & API Gateway"]
        L2["⚙️ Business Logic Layer<br/>Core Services & Workflows"]
        L3["💾 Data Layer<br/>Storage & Data Access"]
        L4["🔗 Integration Layer<br/>External Connections"]
        L5["🌐 External Systems<br/>Third-party Services"]
        L6["🛠️ Technology Stack<br/>Implementation Technologies"]
    end

    %% Apply legend styling
    class L1 presentationLayer
    class L2 businessLayer
    class L3 dataLayer
    class L4 integrationLayer
    class L5 external
    class L6 technology