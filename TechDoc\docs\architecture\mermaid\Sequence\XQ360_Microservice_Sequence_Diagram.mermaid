sequenceDiagram
    participant Client as 🌐 Web Client
    participant Subdomain as 🔗 Subdomain Middleware
    participant Auth as 🔐 Access Control Middleware
    participant API as 🚪 API Controller
    participant Business as ⚙️ Business Component
    participant Queue as 📬 Queue Service
    participant ServiceBus as ☁️ Azure Service Bus
    participant Function as ⚡ Azure Function
    participant NHibernate as 🔧 NHibernate ORM
    participant SQL as 🗄️ SQL Server
    participant Redis as ⚡ Redis Cache
    participant IoTHub as 📡 Azure IoT Hub
    participant Device as 📱 IoT Device
    participant Notification as 📢 Notification Service

    %% Vehicle Access Creation Flow
    rect rgb(232, 245, 233)
        Note over Client,Device: Vehicle Access Creation Process
        
        Client->>Subdomain: POST /api/vehicleaccesscreation/create
        Note over Subdomain: Extract subdomain, validate dealer
        
        Subdomain->>Auth: Forward Request
        Note over Auth: Validate JWT Token, Check Permissions
        
        Auth->>API: Authenticated Request
        API->>Business: CreateVehicleAccessAsync(vehicleData)
        
        Business->>NHibernate: Save Vehicle Access Data
        NHibernate->>SQL: INSERT/UPDATE Operations
        SQL-->>NHibernate: Success Response
        NHibernate-->>Business: Vehicle Access Created
        
        Business->>Queue: SendVehicleAccessCreationMessageAsync()
        Queue->>ServiceBus: Send Message to Queue
        ServiceBus-->>Queue: Message Accepted
        Queue-->>Business: Message Queued Successfully
        
        Business-->>API: Return Success Response
        API-->>Auth: Return ActionResult
        Auth-->>Subdomain: Forward Response
        Subdomain-->>Client: HTTP 200 OK
        
        %% Asynchronous Processing
        ServiceBus->>Function: Trigger VehicleAccessProcessor
        Function->>Business: CreateVehicleAccessAsync(message)
        Business->>IoTHub: SyncDriverToVehicleAsync(deviceId, data)
        IoTHub->>Device: Send Device Twin Update
        Device-->>IoTHub: Acknowledgment
        IoTHub-->>Business: Sync Complete
        Business-->>Function: Processing Complete
    end

    %% User Access Update Flow
    rect rgb(255, 243, 224)
        Note over Client,Device: User Access Update Process
        
        Client->>Subdomain: PUT /api/vehicleaccesscreation/manageuseraccess
        Subdomain->>Auth: Forward Request
        Auth->>API: Authenticated Request
        API->>Business: UpdateAccessesForPersonAsync(personId, accesses)
        
        Business->>Queue: SendUserAccessUpdateMessageAsync()
        Queue->>ServiceBus: Send Message to User Access Queue
        ServiceBus-->>Queue: Message Accepted
        Queue-->>Business: Message Queued
        
        Business-->>API: Return Immediate Response
        API-->>Client: HTTP 202 Accepted
        
        %% Asynchronous Processing
        ServiceBus->>Function: Trigger UserAccessProcessor
        Function->>Business: ProcessUserAccessUpdateAsync(message)
        Business->>NHibernate: Update User Access Permissions
        NHibernate->>SQL: UPDATE Operations
        SQL-->>NHibernate: Success Response
        
        Business->>IoTHub: SyncDriverToVehicleAsync(deviceId, updatedAccesses)
        IoTHub->>Device: Update Device Access List
        Device-->>IoTHub: Access Updated
        IoTHub-->>Business: Sync Complete
        
        Business->>Notification: SendAccessUpdateNotification(userId)
        Notification->>Redis: Store Notification Data
        Redis-->>Notification: Stored Successfully
        Notification-->>Business: Notification Sent
        
        Business-->>Function: Processing Complete
    end

    %% Real-time Vehicle Status Flow
    rect rgb(240, 248, 255)
        Note over Client,Device: Real-time Vehicle Status Monitoring
        
        Device->>IoTHub: Send Telemetry Data
        IoTHub->>Business: ProcessTelemetryAsync(deviceId, data)
        Business->>NHibernate: Update Vehicle Status
        NHibernate->>SQL: UPDATE Vehicle Status
        SQL-->>NHibernate: Success Response
        
        Business->>Redis: Cache Vehicle Status
        Redis-->>Business: Status Cached
        
        Business->>Notification: CheckAlertConditions(vehicleId)
        alt Alert Condition Met
            Notification->>Notification: SendRealTimeAlert(alertData)
            Notification-->>Business: Alert Sent
        end
        
        %% Client Polling for Updates
        Client->>Subdomain: GET /api/vehicleapi/status/{vehicleId}
        Subdomain->>Auth: Forward Request
        Auth->>API: Authenticated Request
        API->>Redis: Get Cached Vehicle Status
        alt Cache Hit
            Redis-->>API: Return Cached Status
        else Cache Miss
            API->>NHibernate: Get Vehicle Status
            NHibernate->>SQL: SELECT Vehicle Data
            SQL-->>NHibernate: Vehicle Data
            NHibernate-->>API: Vehicle Status
            API->>Redis: Cache Vehicle Status
        end
        API-->>Client: Return Vehicle Status
    end

    %% Health Check Flow
    rect rgb(255, 235, 238)
        Note over Client,Device: System Health Monitoring
        
        Client->>Function: GET /api/HealthCheck
        Function->>SQL: Test Database Connection
        SQL-->>Function: Connection Status
        
        Function->>Redis: Test Cache Connection
        Redis-->>Function: Connection Status
        
        Function->>ServiceBus: Test Queue Connectivity
        ServiceBus-->>Function: Connection Status
        
        Function->>IoTHub: Test IoT Hub Connection
        IoTHub-->>Function: Connection Status
        
        Function-->>Client: Health Status Response
    end

    %% Error Handling Scenarios
    rect rgb(255, 235, 238)
        Note over Client,Device: Error Handling & Retry Logic
        
        alt Database Connection Failed
            NHibernate->>Business: DatabaseException
            Business->>Queue: Retry with Exponential Backoff
            Queue->>ServiceBus: Send Retry Message
            ServiceBus->>Function: Retry Processing
        end
        
        alt IoT Device Unavailable
            Business->>IoTHub: Device Communication Failed
            IoTHub-->>Business: Device Offline
            Business->>Notification: Log Device Offline Alert
            Business->>Queue: Queue for Retry
        end
        
        alt Service Bus Queue Full
            Queue->>ServiceBus: Send Message
            ServiceBus-->>Queue: Queue Full Error
            Queue->>Business: Throttle Request
            Business-->>API: Return 429 Too Many Requests
        end
    end 