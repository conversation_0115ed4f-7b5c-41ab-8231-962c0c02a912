erDiagram
    %% Core Business Entities
    Customer {
        Guid Id PK
        string CompanyName
        Guid DealerId FK
    }
    
    Dealer {
        Guid Id PK
        string Name
        string Description
        string SubDomain
        string PortalURL
        string ThemeColor
        bool IsAPIEnabled
        bool Active
        short ContractNumber
    }
    
    Site {
        Guid Id PK
        Guid CustomerId FK
        Guid TimezoneId
        string Name
        string Address
        bool UnlockSetting
        int EnableUnlockReasonType
    }
    
    Department {
        Guid Id PK
        Guid CustomerId FK
        string Name
    }
    
    VehicleModel {
        Guid Id PK
        string Name
    }
    
    Vehicle {
        Guid Id PK
        Guid CustomerId FK
        Guid SiteId FK
        Guid ModelId FK
        Guid DepartmentId FK
        Guid DriverId FK
        Guid ModuleId1 FK
        string Description
        string HireNo
        string SerialNo
        string LastSessionId
        string ModuleSwapNote
        string VehicleImageInternalName
        DateTime DehireTime
        DateTime DeletedAtUtc
        DateTime HireTime
        DateTime LastSessionDate
        DateTime LastSessionDateTzAdjusted
        int IDLETimer
        long VehicleImageFileSize
        bool ImpactLockout
        bool IsCanbus
        bool ModuleIsConnected
        bool OnHire
        bool TimeoutEnabled
        byte[] VehicleImage
    }
    
    Module {
        Guid Id PK
        double AmberImpact
        double FSSXMulti
        double FSSSBase
        double RedImpact
        double BlueImpact
        int SyncVersion
        int Calibration
        int Status
        int ModuleType
        string IoTDevice UK
        string TechNumber
        string DeviceTwin
        string RANumber
        string FromDepartment
        string FromSite
        string OldDeviceID
        string Note
        string FromSerial
        string SimCardNumber
        string FromCustomer
        string CCID
        DateTime LastActivityTime
        DateTime CalibrationDate
        DateTime LastUpdateTime
        DateTime CalibrationResetDate
        DateTime CCIDUpdateDateTime
        DateTime SimCardDate
        DateTime SwapDate
        bool IsAllocatedToVehicle
        Guid DealerId FK
        bool IsActive
    }
    
    %% Person and Driver Management
    Person {
        Guid Id PK
        Guid CustomerId FK
        Guid DepartmentId FK
        Guid SiteId FK
        Guid DriverId FK
        string FirstName
        string LastName
        string Email
        bool Supervisor
        bool OnDemand
        bool LicenseActive
        bool VehicleAccess
        bool IsActiveDriver
        short MasterMenuOptions
        DateTime DeletedAtUtc
    }
    
    Driver {
        Guid Id PK
        Guid CardDetailsId FK
        bool Active
        int LicenseMode
        string LastSessionId
        DateTime LastSessionDate
        DateTime LastSessionDateTzAdjusted
        bool VehicleAccess
        Guid LicenceDetailId FK
        Guid CustomerId FK
        Guid DepartmentId FK
        Guid SiteId FK
    }
    
    LicenceDetail {
        Guid Id PK
        DateTime ExpiryDate
        string LicenseNumber
        string Document
        int DocumentFileSize
        string DocumentInternalName
    }
    
    LicenseByModel {
        Guid Id PK
        Guid DriverId FK
        Guid ModelId FK
        DateTime ExpiryDate
        string ModelImage
        int ModelImageFileSize
        string ModelImageInternalName
    }
    
    %% Access Control Entities
    Permission {
        Guid Id PK
        int LevelName
        string Description
    }
    
    Card {
        Guid Id PK
        Guid SiteId FK
        string Weigand
        string CardNumber UK
        string FacilityCode
        bool Active
        int Type
        int KeypadReader
    }
    
    %% Normal Card Access Entities
    SiteVehicleNormalCardAccess {
        Guid Id PK
        Guid SiteId FK
        Guid PermissionId FK
        Guid CardId FK
    }
    
    ModelVehicleNormalCardAccess {
        Guid Id PK
        Guid ModelId FK
        Guid PermissionId FK
        Guid CardId FK
        Guid DepartmentId FK
    }
    
    PerVehicleNormalCardAccess {
        Guid Id PK
        Guid VehicleId FK
        Guid PermissionId FK
        Guid CardId FK
    }
    
    DepartmentVehicleNormalCardAccess {
        Guid Id PK
        Guid CardId FK
        Guid DepartmentId FK
        Guid PermissionId FK
    }
    
    %% Master Card Access Entities
    SiteVehicleMasterCardAccess {
        Guid Id PK
        Guid SiteId FK
        Guid PermissionId FK
        Guid CardId FK
    }
    
    ModelVehicleMasterCardAccess {
        Guid Id PK
        Guid ModelId FK
        Guid PermissionId FK
        Guid CardId FK
        Guid DepartmentId FK
    }
    
    PerVehicleMasterCardAccess {
        Guid Id PK
        Guid VehicleId FK
        Guid PermissionId FK
        Guid CardId FK
        Guid DepartmentVehicleMasterCardAccessId FK
        Guid ModelVehicleMasterCardAccessId FK
        Guid SiteVehicleNormalCardAccessId FK
    }
    
    %% Dealer Management
    GOUser {
        Guid Id PK
        string EmailAddress
        string UserName
        string FullName
        string FirstName
        string LastName
        string Password
        bool EmailValidated
        bool UserValidated
        bool Blocked
        bool Unregistered
        bool DealerAdmin
        Guid DealerId FK
    }
    
    DealerDriver {
        Guid Id PK
        int DriverType
        Guid CardId FK
        Guid GOUserId FK
    }
    
    %% Processing Status
    VehicleProcessingStatus {
        Guid VehicleId PK,FK
        string ProcessingStatus
        string ProcessedBy
        DateTime StartedAt
        DateTime CompletedAt
        DateTime ExpiresAt
    }
    
    %% Relationships - Core Business Hierarchy
    Dealer ||--o{ Customer : "has"
    Customer ||--o{ Site : "has"
    Customer ||--o{ Department : "has"
    Customer ||--o{ Vehicle : "owns"
    Customer ||--o{ Person : "employs"
    
    %% Vehicle Relationships
    Site ||--o{ Vehicle : "contains"
    Department ||--o{ Vehicle : "manages"
    VehicleModel ||--o{ Vehicle : "defines"
    Module ||--o{ Vehicle : "equips"
    
    %% Person and Driver Relationships
    Site ||--o{ Person : "employs"
    Department ||--o{ Person : "manages"
    Driver ||--o{ Person : "is"
    Driver ||--|| Card : "has"
    Driver ||--|| LicenceDetail : "has"
    Driver ||--o{ LicenseByModel : "has"
    VehicleModel ||--o{ LicenseByModel : "requires"
    
    %% Access Control Relationships
    Permission ||--o{ SiteVehicleNormalCardAccess : "defines"
    Permission ||--o{ ModelVehicleNormalCardAccess : "defines"
    Permission ||--o{ PerVehicleNormalCardAccess : "defines"
    Permission ||--o{ DepartmentVehicleNormalCardAccess : "defines"
    Permission ||--o{ SiteVehicleMasterCardAccess : "defines"
    Permission ||--o{ ModelVehicleMasterCardAccess : "defines"
    Permission ||--o{ PerVehicleMasterCardAccess : "defines"
    
    Card ||--o{ SiteVehicleNormalCardAccess : "grants"
    Card ||--o{ ModelVehicleNormalCardAccess : "grants"
    Card ||--o{ PerVehicleNormalCardAccess : "grants"
    Card ||--o{ DepartmentVehicleNormalCardAccess : "grants"
    Card ||--o{ SiteVehicleMasterCardAccess : "grants"
    Card ||--o{ ModelVehicleMasterCardAccess : "grants"
    Card ||--o{ PerVehicleMasterCardAccess : "grants"
    Card ||--o{ DealerDriver : "assigned_to"
    
    Site ||--o{ SiteVehicleNormalCardAccess : "allows"
    Site ||--o{ SiteVehicleMasterCardAccess : "allows"
    VehicleModel ||--o{ ModelVehicleNormalCardAccess : "allows"
    VehicleModel ||--o{ ModelVehicleMasterCardAccess : "allows"
    Vehicle ||--o{ PerVehicleNormalCardAccess : "allows"
    Vehicle ||--o{ PerVehicleMasterCardAccess : "allows"
    Department ||--o{ ModelVehicleNormalCardAccess : "restricts"
    Department ||--o{ ModelVehicleMasterCardAccess : "restricts"
    Department ||--o{ DepartmentVehicleNormalCardAccess : "allows"
    
    %% Master Access Relationships
    SiteVehicleNormalCardAccess ||--o{ PerVehicleMasterCardAccess : "inherits_from"
    
    %% Dealer Management Relationships
    Dealer ||--o{ GOUser : "employs"
    GOUser ||--|| DealerDriver : "is"
    
    %% Processing Status
    Vehicle ||--|| VehicleProcessingStatus : "tracks"
    
    %% Cascade Delete Behaviors (annotated in relationships)
    %% SiteVehicleNormalCardAccess: CASCADE on Site deletion
    %% ModelVehicleNormalCardAccess: CASCADE on Model deletion
    %% PerVehicleNormalCardAccess: CASCADE on Vehicle deletion
    %% LicenseByModel: CASCADE on Driver deletion, RESTRICT on Model deletion
    %% VehicleProcessingStatus: CASCADE on Vehicle deletion 