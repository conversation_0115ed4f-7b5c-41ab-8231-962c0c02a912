stateDiagram-v2
    [*] --> Unauthenticated : User Access
    
    Unauthenticated --> LoginAttempt : Submit Credentials
    LoginAttempt --> Unauthenticated : Invalid Credentials
    LoginAttempt --> Authenticated : Valid Credentials
    
    Authenticated --> SessionActive : Session Created
    Authenticated --> Unauthenticated : Authentication Failed
    
    SessionActive --> SessionExpired : Session Timeout
    SessionActive --> Logout : User Logout
    SessionActive --> Unauthenticated : Token Invalid
    
    SessionExpired --> Unauthenticated : Redirect to Login
    Logout --> Unauthenticated : Session Destroyed
    
    note right of Unauthenticated
        No valid session
        Login required
        Public resources only
    end note
    
    note right of LoginAttempt
        Credential validation
        Password policy check
        Account status verification
    end note
    
    note right of Authenticated
        Credentials validated
        User identity confirmed
        Permissions loaded
    end note
    
    note right of SessionActive
        Valid session token
        Full system access
        Real-time updates
    end note
    
    note right of SessionExpired
        Token expired
        Security timeout
        Re-authentication required
    end note 