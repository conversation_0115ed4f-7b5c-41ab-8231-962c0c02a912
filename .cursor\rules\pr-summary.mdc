---
description: PR Summary Generation Rule
globs:
alwaysApply: false
---

# PR Summary Generation Rule

When the user asks "create a pr summary", provide a structured summary following this format:

## Title
Use a clear, descriptive title that indicates what is being added, updated, or changed

## Summary
Brief explanation of what the PR accomplishes and its purpose

## Changes Made
- List specific files, components, or features included
- Use bullet points for easy readability
- Group related changes under subheadings if applicable

## Documentation Value (if applicable)
- Describe how the changes help developers or users
- Explain benefits for system understanding, onboarding, etc.

## Viewing Instructions (if applicable)
- Include brief instructions on how to view/test the changes
- Mention any tools, extensions, or setup required

## Categories Covered (if applicable)
- Mention different types of changes included
- Group by functionality or component type

## File Structure (if applicable)
- Show directory structure for new files/folders
- Use code blocks with proper formatting

## Next Steps
- Remaining items or future considerations
- Recommendations for similar tasks

## Confidence Level
- Provide confidence level from 0-100% on the summary accuracy
- If lower than 80%, explain what information is missing for more relevant answers

---

**Note**: Adapt the sections based on the specific PR content. Not all sections may be applicable for every PR.
description:
globs:
alwaysApply: false
---
