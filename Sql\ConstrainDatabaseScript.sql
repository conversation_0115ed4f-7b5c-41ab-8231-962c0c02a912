﻿-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CHECK AND TRY TO APPLY MODEL CONSTRAINTS SCRIPT
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 1: Check for NOT NULL constraint for FK dbo.ChecklistDetail.ChecklistResultId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ChecklistDetail]') and name = 'ChecklistResultId') = 1
BEGIN
	IF NOT EXISTS (SELECT ChecklistResultId FROM [dbo].[ChecklistDetail] WHERE ChecklistResultId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ChecklistDetail] 
			ALTER COLUMN [ChecklistResultId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ChecklistDetail.ChecklistResultId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ChecklistDetail.ChecklistResultId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 2: Check for NOT NULL constraint for FK dbo.ChecklistDetail.PreOperationalChecklistId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ChecklistDetail]') and name = 'PreOperationalChecklistId') = 1
BEGIN
	IF NOT EXISTS (SELECT PreOperationalChecklistId FROM [dbo].[ChecklistDetail] WHERE PreOperationalChecklistId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ChecklistDetail] 
			ALTER COLUMN [PreOperationalChecklistId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ChecklistDetail.PreOperationalChecklistId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ChecklistDetail.PreOperationalChecklistId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 3: Check for NOT NULL constraint for FK dbo.VehicleLockout.SessionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleLockout]') and name = 'SessionId') = 1
BEGIN
	IF NOT EXISTS (SELECT SessionId FROM [dbo].[VehicleLockout] WHERE SessionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleLockout] 
			ALTER COLUMN [SessionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleLockout.SessionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleLockout.SessionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 4: Check for NOT NULL constraint for FK dbo.VehicleLockout.DriverId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleLockout]') and name = 'DriverId') = 1
BEGIN
	IF NOT EXISTS (SELECT DriverId FROM [dbo].[VehicleLockout] WHERE DriverId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleLockout] 
			ALTER COLUMN [DriverId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleLockout.DriverId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleLockout.DriverId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 5: Check for NOT NULL constraint for FK dbo.VehicleDiagnostic.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleDiagnostic]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[VehicleDiagnostic] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleDiagnostic] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleDiagnostic.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleDiagnostic.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 6: Check for Unique Constraint U_VehicleDiagnosticVehicle on [dbo].[VehicleDiagnostic] and try to apply if not already done so
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'U_VehicleDiagnosticVehicle' AND object_id = OBJECT_ID('[dbo].[VehicleDiagnostic]'))
BEGIN
	IF (SELECT COUNT (*) FROM [dbo].[VehicleDiagnostic] WHERE [VehicleId] IS NULL) <= 1 
	BEGIN
		CREATE UNIQUE NONCLUSTERED INDEX [U_VehicleDiagnosticVehicle]
		ON [dbo].[VehicleDiagnostic] 
			([VehicleId]) 
		PRINT 'INFO: Unique Constraint U_VehicleDiagnosticVehicle has been applied successfully'
	END
	ELSE
	BEGIN
		PRINT 'WARNING: Failed to apply Unique Constraint on entity table VehicleDiagnostic - there is more than one existing NULL in mandatory column(s): [VehicleId]'
	END
END
GO
-- step 7: Check for NOT NULL constraint for FK dbo.VehicleGPS.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleGPS]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[VehicleGPS] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleGPS] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleGPS.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleGPS.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 8: Check for Unique Constraint U_IoTDeviceIdConstraint on [dbo].[Module] and try to apply if not already done so
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'U_IoTDeviceIdConstraint' AND object_id = OBJECT_ID('[dbo].[Module]'))
BEGIN
	IF (SELECT COUNT (*) FROM [dbo].[Module] WHERE [IoTDevice] IS NULL) <= 1 
	BEGIN
		CREATE UNIQUE NONCLUSTERED INDEX [U_IoTDeviceIdConstraint]
		ON [dbo].[Module] 
			([IoTDevice]) 
		PRINT 'INFO: Unique Constraint U_IoTDeviceIdConstraint has been applied successfully'
	END
	ELSE
	BEGIN
		PRINT 'WARNING: Failed to apply Unique Constraint on entity table Module - there is more than one existing NULL in mandatory column(s): [IoTDevice]'
	END
END
GO
-- step 9: Check for NOT NULL constraint for FK dbo.AccessGroup.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[AccessGroup]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[AccessGroup] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[AccessGroup] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: AccessGroup.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column AccessGroup.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 10: Check for NOT NULL constraint for FK dbo.MessageHistory.GOUserId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[MessageHistory]') and name = 'GOUserId') = 1
BEGIN
	IF NOT EXISTS (SELECT GOUserId FROM [dbo].[MessageHistory] WHERE GOUserId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[MessageHistory] 
			ALTER COLUMN [GOUserId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: MessageHistory.GOUserId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column MessageHistory.GOUserId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 11: Check for NOT NULL constraint for FK dbo.MessageHistory.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[MessageHistory]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[MessageHistory] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[MessageHistory] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: MessageHistory.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column MessageHistory.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 12: Check for NOT NULL constraint for FK dbo.AccessGroupToSite.AccessGroupId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[AccessGroupToSite]') and name = 'AccessGroupId') = 1
BEGIN
	IF NOT EXISTS (SELECT AccessGroupId FROM [dbo].[AccessGroupToSite] WHERE AccessGroupId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[AccessGroupToSite] 
			ALTER COLUMN [AccessGroupId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: AccessGroupToSite.AccessGroupId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column AccessGroupToSite.AccessGroupId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 13: Check for NOT NULL constraint for FK dbo.AccessGroupToSite.SiteId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[AccessGroupToSite]') and name = 'SiteId') = 1
BEGIN
	IF NOT EXISTS (SELECT SiteId FROM [dbo].[AccessGroupToSite] WHERE SiteId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[AccessGroupToSite] 
			ALTER COLUMN [SiteId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: AccessGroupToSite.SiteId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column AccessGroupToSite.SiteId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 14: Check for NOT NULL constraint for FK dbo.Model.DealerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Model]') and name = 'DealerId') = 1
BEGIN
	IF NOT EXISTS (SELECT DealerId FROM [dbo].[Model] WHERE DealerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Model] 
			ALTER COLUMN [DealerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Model.DealerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Model.DealerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 15: Check for NOT NULL constraint for FK dbo.Customer.DealerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Customer]') and name = 'DealerId') = 1
BEGIN
	IF NOT EXISTS (SELECT DealerId FROM [dbo].[Customer] WHERE DealerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Customer] 
			ALTER COLUMN [DealerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Customer.DealerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Customer.DealerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 16: Check for NOT NULL constraint for FK dbo.Customer.CountryId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Customer]') and name = 'CountryId') = 1
BEGIN
	IF NOT EXISTS (SELECT CountryId FROM [dbo].[Customer] WHERE CountryId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Customer] 
			ALTER COLUMN [CountryId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Customer.CountryId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Customer.CountryId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 17: Check for NOT NULL constraint for FK dbo.DepartmentChecklist.DepartmentId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[DepartmentChecklist]') and name = 'DepartmentId') = 1
BEGIN
	IF NOT EXISTS (SELECT DepartmentId FROM [dbo].[DepartmentChecklist] WHERE DepartmentId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[DepartmentChecklist] 
			ALTER COLUMN [DepartmentId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: DepartmentChecklist.DepartmentId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column DepartmentChecklist.DepartmentId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 18: Check for NOT NULL constraint for FK dbo.ModuleHistory.ModuleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ModuleHistory]') and name = 'ModuleId') = 1
BEGIN
	IF NOT EXISTS (SELECT ModuleId FROM [dbo].[ModuleHistory] WHERE ModuleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ModuleHistory] 
			ALTER COLUMN [ModuleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ModuleHistory.ModuleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ModuleHistory.ModuleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 19: Check for NOT NULL constraint for FK dbo.GPSHistory.SessionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[GPSHistory]') and name = 'SessionId') = 1
BEGIN
	IF NOT EXISTS (SELECT SessionId FROM [dbo].[GPSHistory] WHERE SessionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[GPSHistory] 
			ALTER COLUMN [SessionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: GPSHistory.SessionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GPSHistory.SessionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 20: Check for NOT NULL constraint for FK dbo.CardToCardAccess.CardDetailsId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[CardToCardAccess]') and name = 'CardDetailsId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardDetailsId FROM [dbo].[CardToCardAccess] WHERE CardDetailsId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[CardToCardAccess] 
			ALTER COLUMN [CardDetailsId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CardToCardAccess.CardDetailsId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CardToCardAccess.CardDetailsId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 21: Check for NOT NULL constraint for FK dbo.CardToCardAccess.PermissionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[CardToCardAccess]') and name = 'PermissionId') = 1
BEGIN
	IF NOT EXISTS (SELECT PermissionId FROM [dbo].[CardToCardAccess] WHERE PermissionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[CardToCardAccess] 
			ALTER COLUMN [PermissionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CardToCardAccess.PermissionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CardToCardAccess.PermissionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 22: Check for NOT NULL constraint for FK dbo.ZoneCoordinates.FloorZonesId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ZoneCoordinates]') and name = 'FloorZonesId') = 1
BEGIN
	IF NOT EXISTS (SELECT FloorZonesId FROM [dbo].[ZoneCoordinates] WHERE FloorZonesId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ZoneCoordinates] 
			ALTER COLUMN [FloorZonesId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ZoneCoordinates.FloorZonesId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ZoneCoordinates.FloorZonesId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 23: Check for NOT NULL constraint for FK GOChangeTracking.Snapshot.fkRevisionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOChangeTracking].[Snapshot]') and name = 'fkRevisionId') = 1
BEGIN
	IF NOT EXISTS (SELECT fkRevisionId FROM [GOChangeTracking].[Snapshot] WHERE fkRevisionId is NULL)
	BEGIN	
		ALTER TABLE [GOChangeTracking].[Snapshot] 
			ALTER COLUMN [fkRevisionId] [int] NOT NULL 
		PRINT 'INFO: Snapshot.fkRevisionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Snapshot.fkRevisionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 24: Check for NOT NULL constraint for FK dbo.DepartmentVehicleNormalCardAccess.CardId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[DepartmentVehicleNormalCardAccess]') and name = 'CardId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardId FROM [dbo].[DepartmentVehicleNormalCardAccess] WHERE CardId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[DepartmentVehicleNormalCardAccess] 
			ALTER COLUMN [CardId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: DepartmentVehicleNormalCardAccess.CardId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column DepartmentVehicleNormalCardAccess.CardId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 25: Check for NOT NULL constraint for FK dbo.DepartmentVehicleNormalCardAccess.DepartmentId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[DepartmentVehicleNormalCardAccess]') and name = 'DepartmentId') = 1
BEGIN
	IF NOT EXISTS (SELECT DepartmentId FROM [dbo].[DepartmentVehicleNormalCardAccess] WHERE DepartmentId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[DepartmentVehicleNormalCardAccess] 
			ALTER COLUMN [DepartmentId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: DepartmentVehicleNormalCardAccess.DepartmentId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column DepartmentVehicleNormalCardAccess.DepartmentId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 26: Check for NOT NULL constraint for FK dbo.DepartmentVehicleNormalCardAccess.PermissionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[DepartmentVehicleNormalCardAccess]') and name = 'PermissionId') = 1
BEGIN
	IF NOT EXISTS (SELECT PermissionId FROM [dbo].[DepartmentVehicleNormalCardAccess] WHERE PermissionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[DepartmentVehicleNormalCardAccess] 
			ALTER COLUMN [PermissionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: DepartmentVehicleNormalCardAccess.PermissionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column DepartmentVehicleNormalCardAccess.PermissionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 27: Check for NOT NULL constraint for FK dbo.VehicleSlamcoreLocationHistory.SlamcoreDeviceId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleSlamcoreLocationHistory]') and name = 'SlamcoreDeviceId') = 1
BEGIN
	IF NOT EXISTS (SELECT SlamcoreDeviceId FROM [dbo].[VehicleSlamcoreLocationHistory] WHERE SlamcoreDeviceId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleSlamcoreLocationHistory] 
			ALTER COLUMN [SlamcoreDeviceId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleSlamcoreLocationHistory.SlamcoreDeviceId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleSlamcoreLocationHistory.SlamcoreDeviceId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 28: Check for NOT NULL constraint for FK dbo.SessionDetails.SessionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SessionDetails]') and name = 'SessionId') = 1
BEGIN
	IF NOT EXISTS (SELECT SessionId FROM [dbo].[SessionDetails] WHERE SessionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SessionDetails] 
			ALTER COLUMN [SessionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SessionDetails.SessionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SessionDetails.SessionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 29: Check for NOT NULL constraint for FK dbo.SessionDetails.IOFIELDId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SessionDetails]') and name = 'IOFIELDId') = 1
BEGIN
	IF NOT EXISTS (SELECT IOFIELDId FROM [dbo].[SessionDetails] WHERE IOFIELDId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SessionDetails] 
			ALTER COLUMN [IOFIELDId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SessionDetails.IOFIELDId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SessionDetails.IOFIELDId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 30: Check for NOT NULL constraint for FK dbo.Vehicle.ModelId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Vehicle]') and name = 'ModelId') = 1
BEGIN
	IF NOT EXISTS (SELECT ModelId FROM [dbo].[Vehicle] WHERE ModelId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Vehicle] 
			ALTER COLUMN [ModelId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Vehicle.ModelId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Vehicle.ModelId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 31: Check for NOT NULL constraint for FK dbo.Vehicle.DepartmentId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Vehicle]') and name = 'DepartmentId') = 1
BEGIN
	IF NOT EXISTS (SELECT DepartmentId FROM [dbo].[Vehicle] WHERE DepartmentId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Vehicle] 
			ALTER COLUMN [DepartmentId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Vehicle.DepartmentId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Vehicle.DepartmentId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 32: Check for NOT NULL constraint for FK dbo.Vehicle.SiteId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Vehicle]') and name = 'SiteId') = 1
BEGIN
	IF NOT EXISTS (SELECT SiteId FROM [dbo].[Vehicle] WHERE SiteId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Vehicle] 
			ALTER COLUMN [SiteId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Vehicle.SiteId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Vehicle.SiteId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 33: Check for NOT NULL constraint for FK dbo.Vehicle.ModuleId1 and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Vehicle]') and name = 'ModuleId1') = 1
BEGIN
	IF NOT EXISTS (SELECT ModuleId1 FROM [dbo].[Vehicle] WHERE ModuleId1 is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Vehicle] 
			ALTER COLUMN [ModuleId1] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Vehicle.ModuleId1 has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Vehicle.ModuleId1 because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 34: Check for NOT NULL constraint for FK dbo.Vehicle.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Vehicle]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[Vehicle] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Vehicle] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Vehicle.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Vehicle.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 35: Check for NOT NULL constraint for FK dbo.EmailGroupsToPerson.EmailGroupsId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[EmailGroupsToPerson]') and name = 'EmailGroupsId') = 1
BEGIN
	IF NOT EXISTS (SELECT EmailGroupsId FROM [dbo].[EmailGroupsToPerson] WHERE EmailGroupsId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[EmailGroupsToPerson] 
			ALTER COLUMN [EmailGroupsId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: EmailGroupsToPerson.EmailGroupsId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column EmailGroupsToPerson.EmailGroupsId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 36: Check for NOT NULL constraint for FK dbo.EmailGroupsToPerson.PersonId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[EmailGroupsToPerson]') and name = 'PersonId') = 1
BEGIN
	IF NOT EXISTS (SELECT PersonId FROM [dbo].[EmailGroupsToPerson] WHERE PersonId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[EmailGroupsToPerson] 
			ALTER COLUMN [PersonId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: EmailGroupsToPerson.PersonId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column EmailGroupsToPerson.PersonId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 37: Check for NOT NULL constraint for FK GOSecurity.GOGroupRole.GORoleName and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOSecurity].[GOGroupRole]') and name = 'GORoleName') = 1
BEGIN
	IF NOT EXISTS (SELECT GORoleName FROM [GOSecurity].[GOGroupRole] WHERE GORoleName is NULL)
	BEGIN	
		ALTER TABLE [GOSecurity].[GOGroupRole] 
			ALTER COLUMN [GORoleName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
		PRINT 'INFO: GOGroupRole.GORoleName has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GOGroupRole.GORoleName because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 38: Check for NOT NULL constraint for FK GOSecurity.GOGroupRole.GOGroupName and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOSecurity].[GOGroupRole]') and name = 'GOGroupName') = 1
BEGIN
	IF NOT EXISTS (SELECT GOGroupName FROM [GOSecurity].[GOGroupRole] WHERE GOGroupName is NULL)
	BEGIN	
		ALTER TABLE [GOSecurity].[GOGroupRole] 
			ALTER COLUMN [GOGroupName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
		PRINT 'INFO: GOGroupRole.GOGroupName has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GOGroupRole.GOGroupName because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 39: Check for NOT NULL constraint for FK dbo.ModelVehicleMasterCardAccess.ModelId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ModelVehicleMasterCardAccess]') and name = 'ModelId') = 1
BEGIN
	IF NOT EXISTS (SELECT ModelId FROM [dbo].[ModelVehicleMasterCardAccess] WHERE ModelId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ModelVehicleMasterCardAccess] 
			ALTER COLUMN [ModelId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ModelVehicleMasterCardAccess.ModelId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ModelVehicleMasterCardAccess.ModelId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 40: Check for NOT NULL constraint for FK dbo.ModelVehicleMasterCardAccess.CardId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ModelVehicleMasterCardAccess]') and name = 'CardId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardId FROM [dbo].[ModelVehicleMasterCardAccess] WHERE CardId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ModelVehicleMasterCardAccess] 
			ALTER COLUMN [CardId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ModelVehicleMasterCardAccess.CardId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ModelVehicleMasterCardAccess.CardId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 41: Check for NOT NULL constraint for FK dbo.AlertHistory.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[AlertHistory]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[AlertHistory] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[AlertHistory] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: AlertHistory.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column AlertHistory.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 42: Check for NOT NULL constraint for FK dbo.AlertHistory.AlertId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[AlertHistory]') and name = 'AlertId') = 1
BEGIN
	IF NOT EXISTS (SELECT AlertId FROM [dbo].[AlertHistory] WHERE AlertId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[AlertHistory] 
			ALTER COLUMN [AlertId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: AlertHistory.AlertId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column AlertHistory.AlertId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 43: Check for NOT NULL constraint for FK dbo.AlertHistory.DriverId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[AlertHistory]') and name = 'DriverId') = 1
BEGIN
	IF NOT EXISTS (SELECT DriverId FROM [dbo].[AlertHistory] WHERE DriverId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[AlertHistory] 
			ALTER COLUMN [DriverId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: AlertHistory.DriverId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column AlertHistory.DriverId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 44: Check for NOT NULL constraint for FK dbo.WebsiteRole.WebsiteUserId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[WebsiteRole]') and name = 'WebsiteUserId') = 1
BEGIN
	IF NOT EXISTS (SELECT WebsiteUserId FROM [dbo].[WebsiteRole] WHERE WebsiteUserId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[WebsiteRole] 
			ALTER COLUMN [WebsiteUserId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: WebsiteRole.WebsiteUserId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column WebsiteRole.WebsiteUserId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 45: Check for NOT NULL constraint for FK GOSecurity.GOUserRole.GOUserId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOSecurity].[GOUserRole]') and name = 'GOUserId') = 1
BEGIN
	IF NOT EXISTS (SELECT GOUserId FROM [GOSecurity].[GOUserRole] WHERE GOUserId is NULL)
	BEGIN	
		ALTER TABLE [GOSecurity].[GOUserRole] 
			ALTER COLUMN [GOUserId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: GOUserRole.GOUserId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GOUserRole.GOUserId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 46: Check for NOT NULL constraint for FK GOSecurity.GOUserRole.GORoleName and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOSecurity].[GOUserRole]') and name = 'GORoleName') = 1
BEGIN
	IF NOT EXISTS (SELECT GORoleName FROM [GOSecurity].[GOUserRole] WHERE GORoleName is NULL)
	BEGIN	
		ALTER TABLE [GOSecurity].[GOUserRole] 
			ALTER COLUMN [GORoleName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
		PRINT 'INFO: GOUserRole.GORoleName has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GOUserRole.GORoleName because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 47: Check for NOT NULL constraint for FK dbo.Impact.SessionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Impact]') and name = 'SessionId') = 1
BEGIN
	IF NOT EXISTS (SELECT SessionId FROM [dbo].[Impact] WHERE SessionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Impact] 
			ALTER COLUMN [SessionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Impact.SessionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Impact.SessionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 48: Check for NOT NULL constraint for FK dbo.CustomerModel.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[CustomerModel]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[CustomerModel] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[CustomerModel] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CustomerModel.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CustomerModel.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 49: Check for NOT NULL constraint for FK dbo.CustomerModel.ModelId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[CustomerModel]') and name = 'ModelId') = 1
BEGIN
	IF NOT EXISTS (SELECT ModelId FROM [dbo].[CustomerModel] WHERE ModelId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[CustomerModel] 
			ALTER COLUMN [ModelId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CustomerModel.ModelId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CustomerModel.ModelId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 50: Check for NOT NULL constraint for FK GOChangeTracking.Tag.fkRevisionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOChangeTracking].[Tag]') and name = 'fkRevisionId') = 1
BEGIN
	IF NOT EXISTS (SELECT fkRevisionId FROM [GOChangeTracking].[Tag] WHERE fkRevisionId is NULL)
	BEGIN	
		ALTER TABLE [GOChangeTracking].[Tag] 
			ALTER COLUMN [fkRevisionId] [int] NOT NULL 
		PRINT 'INFO: Tag.fkRevisionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Tag.fkRevisionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 51: Check for NOT NULL constraint for FK dbo.VehicleBroadcastMessage.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleBroadcastMessage]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[VehicleBroadcastMessage] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleBroadcastMessage] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleBroadcastMessage.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleBroadcastMessage.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 52: Check for NOT NULL constraint for FK dbo.VehicleBroadcastMessage.BroadcastMessageId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleBroadcastMessage]') and name = 'BroadcastMessageId') = 1
BEGIN
	IF NOT EXISTS (SELECT BroadcastMessageId FROM [dbo].[VehicleBroadcastMessage] WHERE BroadcastMessageId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleBroadcastMessage] 
			ALTER COLUMN [BroadcastMessageId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleBroadcastMessage.BroadcastMessageId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleBroadcastMessage.BroadcastMessageId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 53: Check for NOT NULL constraint for FK dbo.FloorPlan.SiteId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[FloorPlan]') and name = 'SiteId') = 1
BEGIN
	IF NOT EXISTS (SELECT SiteId FROM [dbo].[FloorPlan] WHERE SiteId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[FloorPlan] 
			ALTER COLUMN [SiteId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: FloorPlan.SiteId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column FloorPlan.SiteId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 54: Check for NOT NULL constraint for FK dbo.ModelVehicleNormalCardAccess.PermissionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ModelVehicleNormalCardAccess]') and name = 'PermissionId') = 1
BEGIN
	IF NOT EXISTS (SELECT PermissionId FROM [dbo].[ModelVehicleNormalCardAccess] WHERE PermissionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] 
			ALTER COLUMN [PermissionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ModelVehicleNormalCardAccess.PermissionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ModelVehicleNormalCardAccess.PermissionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 55: Check for NOT NULL constraint for FK dbo.ModelVehicleNormalCardAccess.CardId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ModelVehicleNormalCardAccess]') and name = 'CardId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardId FROM [dbo].[ModelVehicleNormalCardAccess] WHERE CardId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] 
			ALTER COLUMN [CardId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ModelVehicleNormalCardAccess.CardId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ModelVehicleNormalCardAccess.CardId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 56: Check for NOT NULL constraint for FK dbo.ModelVehicleNormalCardAccess.DepartmentId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ModelVehicleNormalCardAccess]') and name = 'DepartmentId') = 1
BEGIN
	IF NOT EXISTS (SELECT DepartmentId FROM [dbo].[ModelVehicleNormalCardAccess] WHERE DepartmentId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] 
			ALTER COLUMN [DepartmentId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ModelVehicleNormalCardAccess.DepartmentId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ModelVehicleNormalCardAccess.DepartmentId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 57: Check for NOT NULL constraint for FK dbo.ModelVehicleNormalCardAccess.ModelId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ModelVehicleNormalCardAccess]') and name = 'ModelId') = 1
BEGIN
	IF NOT EXISTS (SELECT ModelId FROM [dbo].[ModelVehicleNormalCardAccess] WHERE ModelId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] 
			ALTER COLUMN [ModelId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ModelVehicleNormalCardAccess.ModelId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ModelVehicleNormalCardAccess.ModelId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 58: Check for NOT NULL constraint for FK dbo.SiteVehicleNormalCardAccess.PermissionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SiteVehicleNormalCardAccess]') and name = 'PermissionId') = 1
BEGIN
	IF NOT EXISTS (SELECT PermissionId FROM [dbo].[SiteVehicleNormalCardAccess] WHERE PermissionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SiteVehicleNormalCardAccess] 
			ALTER COLUMN [PermissionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SiteVehicleNormalCardAccess.PermissionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SiteVehicleNormalCardAccess.PermissionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 59: Check for NOT NULL constraint for FK dbo.SiteVehicleNormalCardAccess.SiteId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SiteVehicleNormalCardAccess]') and name = 'SiteId') = 1
BEGIN
	IF NOT EXISTS (SELECT SiteId FROM [dbo].[SiteVehicleNormalCardAccess] WHERE SiteId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SiteVehicleNormalCardAccess] 
			ALTER COLUMN [SiteId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SiteVehicleNormalCardAccess.SiteId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SiteVehicleNormalCardAccess.SiteId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 60: Check for NOT NULL constraint for FK dbo.SiteVehicleNormalCardAccess.CardId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SiteVehicleNormalCardAccess]') and name = 'CardId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardId FROM [dbo].[SiteVehicleNormalCardAccess] WHERE CardId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SiteVehicleNormalCardAccess] 
			ALTER COLUMN [CardId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SiteVehicleNormalCardAccess.CardId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SiteVehicleNormalCardAccess.CardId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 61: Check for NOT NULL constraint for FK dbo.PersonAllocation.PersonId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PersonAllocation]') and name = 'PersonId') = 1
BEGIN
	IF NOT EXISTS (SELECT PersonId FROM [dbo].[PersonAllocation] WHERE PersonId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PersonAllocation] 
			ALTER COLUMN [PersonId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PersonAllocation.PersonId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PersonAllocation.PersonId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 62: Check for NOT NULL constraint for FK dbo.PersonAllocation.SiteId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PersonAllocation]') and name = 'SiteId') = 1
BEGIN
	IF NOT EXISTS (SELECT SiteId FROM [dbo].[PersonAllocation] WHERE SiteId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PersonAllocation] 
			ALTER COLUMN [SiteId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PersonAllocation.SiteId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PersonAllocation.SiteId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 63: Check for NOT NULL constraint for FK dbo.ChecklistResult.SessionId1 and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ChecklistResult]') and name = 'SessionId1') = 1
BEGIN
	IF NOT EXISTS (SELECT SessionId1 FROM [dbo].[ChecklistResult] WHERE SessionId1 is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ChecklistResult] 
			ALTER COLUMN [SessionId1] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ChecklistResult.SessionId1 has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ChecklistResult.SessionId1 because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 64: Check for NOT NULL constraint for FK dbo.VehicleAlertSubscription.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleAlertSubscription]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[VehicleAlertSubscription] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleAlertSubscription] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleAlertSubscription.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleAlertSubscription.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 65: Check for NOT NULL constraint for FK dbo.VehicleAlertSubscription.AlertSubscriptionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleAlertSubscription]') and name = 'AlertSubscriptionId') = 1
BEGIN
	IF NOT EXISTS (SELECT AlertSubscriptionId FROM [dbo].[VehicleAlertSubscription] WHERE AlertSubscriptionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleAlertSubscription] 
			ALTER COLUMN [AlertSubscriptionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleAlertSubscription.AlertSubscriptionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleAlertSubscription.AlertSubscriptionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 66: Check for NOT NULL constraint for FK dbo.Session.DriverId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Session]') and name = 'DriverId') = 1
BEGIN
	IF NOT EXISTS (SELECT DriverId FROM [dbo].[Session] WHERE DriverId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Session] 
			ALTER COLUMN [DriverId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Session.DriverId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Session.DriverId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 67: Check for NOT NULL constraint for FK dbo.Session.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Session]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[Session] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Session] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Session.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Session.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 68: Check for NOT NULL constraint for FK dbo.Dealer.RegionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Dealer]') and name = 'RegionId') = 1
BEGIN
	IF NOT EXISTS (SELECT RegionId FROM [dbo].[Dealer] WHERE RegionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Dealer] 
			ALTER COLUMN [RegionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Dealer.RegionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Dealer.RegionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 69: Check for Unique Constraint U_UserNameUniqueConstraint on [GOSecurity].[GOUser] and try to apply if not already done so
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'U_UserNameUniqueConstraint' AND object_id = OBJECT_ID('[GOSecurity].[GOUser]'))
BEGIN
	IF (SELECT COUNT (*) FROM [GOSecurity].[GOUser] WHERE [UserName] IS NULL) <= 1 
	BEGIN
		CREATE UNIQUE NONCLUSTERED INDEX [U_UserNameUniqueConstraint]
		ON [GOSecurity].[GOUser] 
			([UserName]) 
		PRINT 'INFO: Unique Constraint U_UserNameUniqueConstraint has been applied successfully'
	END
	ELSE
	BEGIN
		PRINT 'WARNING: Failed to apply Unique Constraint on entity table GOUser - there is more than one existing NULL in mandatory column(s): [UserName]'
	END
END
GO
-- step 70: Check for NOT NULL constraint for FK dbo.UpdateFirmwareRequest.FirmwareId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[UpdateFirmwareRequest]') and name = 'FirmwareId') = 1
BEGIN
	IF NOT EXISTS (SELECT FirmwareId FROM [dbo].[UpdateFirmwareRequest] WHERE FirmwareId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[UpdateFirmwareRequest] 
			ALTER COLUMN [FirmwareId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: UpdateFirmwareRequest.FirmwareId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column UpdateFirmwareRequest.FirmwareId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 71: Check for NOT NULL constraint for FK dbo.GOUserDepartment.GOUserId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[GOUserDepartment]') and name = 'GOUserId') = 1
BEGIN
	IF NOT EXISTS (SELECT GOUserId FROM [dbo].[GOUserDepartment] WHERE GOUserId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[GOUserDepartment] 
			ALTER COLUMN [GOUserId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: GOUserDepartment.GOUserId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GOUserDepartment.GOUserId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 72: Check for NOT NULL constraint for FK dbo.GOUserDepartment.DepartmentId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[GOUserDepartment]') and name = 'DepartmentId') = 1
BEGIN
	IF NOT EXISTS (SELECT DepartmentId FROM [dbo].[GOUserDepartment] WHERE DepartmentId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[GOUserDepartment] 
			ALTER COLUMN [DepartmentId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: GOUserDepartment.DepartmentId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GOUserDepartment.DepartmentId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 73: Check for NOT NULL constraint for FK dbo.SlamcoreDeviceHistory.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SlamcoreDeviceHistory]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[SlamcoreDeviceHistory] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SlamcoreDeviceHistory] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SlamcoreDeviceHistory.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SlamcoreDeviceHistory.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 74: Check for NOT NULL constraint for FK dbo.SlamcoreDeviceHistory.SlamcoreDeviceId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SlamcoreDeviceHistory]') and name = 'SlamcoreDeviceId') = 1
BEGIN
	IF NOT EXISTS (SELECT SlamcoreDeviceId FROM [dbo].[SlamcoreDeviceHistory] WHERE SlamcoreDeviceId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SlamcoreDeviceHistory] 
			ALTER COLUMN [SlamcoreDeviceId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SlamcoreDeviceHistory.SlamcoreDeviceId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SlamcoreDeviceHistory.SlamcoreDeviceId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 75: Check for NOT NULL constraint for FK dbo.VehicleHireDehireHistory.DepartmentId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleHireDehireHistory]') and name = 'DepartmentId') = 1
BEGIN
	IF NOT EXISTS (SELECT DepartmentId FROM [dbo].[VehicleHireDehireHistory] WHERE DepartmentId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleHireDehireHistory] 
			ALTER COLUMN [DepartmentId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleHireDehireHistory.DepartmentId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleHireDehireHistory.DepartmentId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 76: Check for NOT NULL constraint for FK dbo.VehicleHireDehireHistory.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleHireDehireHistory]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[VehicleHireDehireHistory] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleHireDehireHistory] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleHireDehireHistory.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleHireDehireHistory.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 77: Check for NOT NULL constraint for FK dbo.ReportSubscription.ReportTypeId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[ReportSubscription]') and name = 'ReportTypeId') = 1
BEGIN
	IF NOT EXISTS (SELECT ReportTypeId FROM [dbo].[ReportSubscription] WHERE ReportTypeId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[ReportSubscription] 
			ALTER COLUMN [ReportTypeId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ReportSubscription.ReportTypeId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ReportSubscription.ReportTypeId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 78: Check for NOT NULL constraint for FK dbo.PSTATDetails.SessionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PSTATDetails]') and name = 'SessionId') = 1
BEGIN
	IF NOT EXISTS (SELECT SessionId FROM [dbo].[PSTATDetails] WHERE SessionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PSTATDetails] 
			ALTER COLUMN [SessionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PSTATDetails.SessionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PSTATDetails.SessionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 79: Check for NOT NULL constraint for FK dbo.PSTATDetails.IOFIELDId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PSTATDetails]') and name = 'IOFIELDId') = 1
BEGIN
	IF NOT EXISTS (SELECT IOFIELDId FROM [dbo].[PSTATDetails] WHERE IOFIELDId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PSTATDetails] 
			ALTER COLUMN [IOFIELDId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PSTATDetails.IOFIELDId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PSTATDetails.IOFIELDId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 80: Check for NOT NULL constraint for FK dbo.FloorZones.FloorPlanId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[FloorZones]') and name = 'FloorPlanId') = 1
BEGIN
	IF NOT EXISTS (SELECT FloorPlanId FROM [dbo].[FloorZones] WHERE FloorPlanId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[FloorZones] 
			ALTER COLUMN [FloorPlanId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: FloorZones.FloorPlanId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column FloorZones.FloorPlanId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 81: Check for NOT NULL constraint for FK dbo.CanruleDetails.CanruleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[CanruleDetails]') and name = 'CanruleId') = 1
BEGIN
	IF NOT EXISTS (SELECT CanruleId FROM [dbo].[CanruleDetails] WHERE CanruleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[CanruleDetails] 
			ALTER COLUMN [CanruleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CanruleDetails.CanruleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CanruleDetails.CanruleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 82: Check for NOT NULL constraint for FK dbo.DepartmentVehicleMasterCardAccess.CardId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[DepartmentVehicleMasterCardAccess]') and name = 'CardId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardId FROM [dbo].[DepartmentVehicleMasterCardAccess] WHERE CardId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[DepartmentVehicleMasterCardAccess] 
			ALTER COLUMN [CardId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: DepartmentVehicleMasterCardAccess.CardId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column DepartmentVehicleMasterCardAccess.CardId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 83: Check for NOT NULL constraint for FK dbo.DepartmentVehicleMasterCardAccess.DepartmentId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[DepartmentVehicleMasterCardAccess]') and name = 'DepartmentId') = 1
BEGIN
	IF NOT EXISTS (SELECT DepartmentId FROM [dbo].[DepartmentVehicleMasterCardAccess] WHERE DepartmentId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[DepartmentVehicleMasterCardAccess] 
			ALTER COLUMN [DepartmentId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: DepartmentVehicleMasterCardAccess.DepartmentId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column DepartmentVehicleMasterCardAccess.DepartmentId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 84: Check for NOT NULL constraint for FK dbo.VehicleSessionlessImpact.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VehicleSessionlessImpact]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[VehicleSessionlessImpact] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VehicleSessionlessImpact] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VehicleSessionlessImpact.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VehicleSessionlessImpact.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 85: Check for NOT NULL constraint for FK dbo.LicenseByModel.DriverId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[LicenseByModel]') and name = 'DriverId') = 1
BEGIN
	IF NOT EXISTS (SELECT DriverId FROM [dbo].[LicenseByModel] WHERE DriverId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[LicenseByModel] 
			ALTER COLUMN [DriverId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: LicenseByModel.DriverId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column LicenseByModel.DriverId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 86: Check for NOT NULL constraint for FK dbo.LicenseByModel.ModelId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[LicenseByModel]') and name = 'ModelId') = 1
BEGIN
	IF NOT EXISTS (SELECT ModelId FROM [dbo].[LicenseByModel] WHERE ModelId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[LicenseByModel] 
			ALTER COLUMN [ModelId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: LicenseByModel.ModelId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column LicenseByModel.ModelId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 87: Check for NOT NULL constraint for FK GOChangeTracking.CustomerSnapshot.fkCustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOChangeTracking].[CustomerSnapshot]') and name = 'fkCustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT fkCustomerId FROM [GOChangeTracking].[CustomerSnapshot] WHERE fkCustomerId is NULL)
	BEGIN	
		ALTER TABLE [GOChangeTracking].[CustomerSnapshot] 
			ALTER COLUMN [fkCustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CustomerSnapshot.fkCustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CustomerSnapshot.fkCustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 88: Check for NOT NULL constraint for FK dbo.DealerDriver.GOUserId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[DealerDriver]') and name = 'GOUserId') = 1
BEGIN
	IF NOT EXISTS (SELECT GOUserId FROM [dbo].[DealerDriver] WHERE GOUserId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[DealerDriver] 
			ALTER COLUMN [GOUserId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: DealerDriver.GOUserId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column DealerDriver.GOUserId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 89: Check for Unique Constraint U_DealerDriverGOUser on [dbo].[DealerDriver] and try to apply if not already done so
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'U_DealerDriverGOUser' AND object_id = OBJECT_ID('[dbo].[DealerDriver]'))
BEGIN
	IF (SELECT COUNT (*) FROM [dbo].[DealerDriver] WHERE [GOUserId] IS NULL) <= 1 
	BEGIN
		CREATE UNIQUE NONCLUSTERED INDEX [U_DealerDriverGOUser]
		ON [dbo].[DealerDriver] 
			([GOUserId]) 
		PRINT 'INFO: Unique Constraint U_DealerDriverGOUser has been applied successfully'
	END
	ELSE
	BEGIN
		PRINT 'WARNING: Failed to apply Unique Constraint on entity table DealerDriver - there is more than one existing NULL in mandatory column(s): [GOUserId]'
	END
END
GO
-- step 90: Check for NOT NULL constraint for FK dbo.EmailGroups.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[EmailGroups]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[EmailGroups] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[EmailGroups] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: EmailGroups.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column EmailGroups.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 91: Check for NOT NULL constraint for FK dbo.SlamcoreDevice.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SlamcoreDevice]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[SlamcoreDevice] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SlamcoreDevice] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SlamcoreDevice.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SlamcoreDevice.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 92: Check for NOT NULL constraint for FK dbo.Site.TimezoneId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Site]') and name = 'TimezoneId') = 1
BEGIN
	IF NOT EXISTS (SELECT TimezoneId FROM [dbo].[Site] WHERE TimezoneId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Site] 
			ALTER COLUMN [TimezoneId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Site.TimezoneId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Site.TimezoneId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 93: Check for NOT NULL constraint for FK dbo.Site.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Site]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[Site] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Site] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Site.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Site.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 94: Check for NOT NULL constraint for FK dbo.AlertSubscription.PersonId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[AlertSubscription]') and name = 'PersonId') = 1
BEGIN
	IF NOT EXISTS (SELECT PersonId FROM [dbo].[AlertSubscription] WHERE PersonId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[AlertSubscription] 
			ALTER COLUMN [PersonId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: AlertSubscription.PersonId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column AlertSubscription.PersonId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 95: Check for NOT NULL constraint for FK dbo.AlertSubscription.AlertId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[AlertSubscription]') and name = 'AlertId') = 1
BEGIN
	IF NOT EXISTS (SELECT AlertId FROM [dbo].[AlertSubscription] WHERE AlertId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[AlertSubscription] 
			ALTER COLUMN [AlertId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: AlertSubscription.AlertId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column AlertSubscription.AlertId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 96: Check for NOT NULL constraint for FK GOChangeTracking.CustomerAudit.fkCustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOChangeTracking].[CustomerAudit]') and name = 'fkCustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT fkCustomerId FROM [GOChangeTracking].[CustomerAudit] WHERE fkCustomerId is NULL)
	BEGIN	
		ALTER TABLE [GOChangeTracking].[CustomerAudit] 
			ALTER COLUMN [fkCustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CustomerAudit.fkCustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CustomerAudit.fkCustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 97: Check for NOT NULL constraint for FK dbo.PerVehicleNormalCardAccess.PermissionId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PerVehicleNormalCardAccess]') and name = 'PermissionId') = 1
BEGIN
	IF NOT EXISTS (SELECT PermissionId FROM [dbo].[PerVehicleNormalCardAccess] WHERE PermissionId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PerVehicleNormalCardAccess] 
			ALTER COLUMN [PermissionId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PerVehicleNormalCardAccess.PermissionId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PerVehicleNormalCardAccess.PermissionId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 98: Check for NOT NULL constraint for FK dbo.PerVehicleNormalCardAccess.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PerVehicleNormalCardAccess]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[PerVehicleNormalCardAccess] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PerVehicleNormalCardAccess] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PerVehicleNormalCardAccess.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PerVehicleNormalCardAccess.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 99: Check for NOT NULL constraint for FK dbo.PerVehicleNormalCardAccess.CardId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PerVehicleNormalCardAccess]') and name = 'CardId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardId FROM [dbo].[PerVehicleNormalCardAccess] WHERE CardId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PerVehicleNormalCardAccess] 
			ALTER COLUMN [CardId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PerVehicleNormalCardAccess.CardId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PerVehicleNormalCardAccess.CardId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 100: Check for NOT NULL constraint for FK dbo.PedestrianDetectionHistory.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PedestrianDetectionHistory]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[PedestrianDetectionHistory] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PedestrianDetectionHistory] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PedestrianDetectionHistory.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PedestrianDetectionHistory.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 101: Check for NOT NULL constraint for FK ImportExport.ImportJobLog.ImportJobStatusId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[ImportExport].[ImportJobLog]') and name = 'ImportJobStatusId') = 1
BEGIN
	IF NOT EXISTS (SELECT ImportJobStatusId FROM [ImportExport].[ImportJobLog] WHERE ImportJobStatusId is NULL)
	BEGIN	
		ALTER TABLE [ImportExport].[ImportJobLog] 
			ALTER COLUMN [ImportJobStatusId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: ImportJobLog.ImportJobStatusId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column ImportJobLog.ImportJobStatusId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 102: Check for NOT NULL constraint for FK dbo.OnDemandSession.DriverId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[OnDemandSession]') and name = 'DriverId') = 1
BEGIN
	IF NOT EXISTS (SELECT DriverId FROM [dbo].[OnDemandSession] WHERE DriverId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[OnDemandSession] 
			ALTER COLUMN [DriverId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: OnDemandSession.DriverId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column OnDemandSession.DriverId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 103: Check for NOT NULL constraint for FK dbo.OnDemandSession.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[OnDemandSession]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[OnDemandSession] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[OnDemandSession] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: OnDemandSession.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column OnDemandSession.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 104: Check for NOT NULL constraint for FK dbo.VORSettingHistory.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[VORSettingHistory]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[VORSettingHistory] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[VORSettingHistory] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: VORSettingHistory.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column VORSettingHistory.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 105: Check for NOT NULL constraint for FK dbo.CustomerSSODetail.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[CustomerSSODetail]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[CustomerSSODetail] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[CustomerSSODetail] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CustomerSSODetail.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CustomerSSODetail.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 106: Check for NOT NULL constraint for FK dbo.SiteVehicleMasterCardAccess.SiteId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SiteVehicleMasterCardAccess]') and name = 'SiteId') = 1
BEGIN
	IF NOT EXISTS (SELECT SiteId FROM [dbo].[SiteVehicleMasterCardAccess] WHERE SiteId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SiteVehicleMasterCardAccess] 
			ALTER COLUMN [SiteId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SiteVehicleMasterCardAccess.SiteId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SiteVehicleMasterCardAccess.SiteId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 107: Check for NOT NULL constraint for FK dbo.SiteVehicleMasterCardAccess.CardId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[SiteVehicleMasterCardAccess]') and name = 'CardId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardId FROM [dbo].[SiteVehicleMasterCardAccess] WHERE CardId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[SiteVehicleMasterCardAccess] 
			ALTER COLUMN [CardId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: SiteVehicleMasterCardAccess.CardId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column SiteVehicleMasterCardAccess.CardId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 108: Check for NOT NULL constraint for FK GOSecurity.GOUserGroup.GOUserId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOSecurity].[GOUserGroup]') and name = 'GOUserId') = 1
BEGIN
	IF NOT EXISTS (SELECT GOUserId FROM [GOSecurity].[GOUserGroup] WHERE GOUserId is NULL)
	BEGIN	
		ALTER TABLE [GOSecurity].[GOUserGroup] 
			ALTER COLUMN [GOUserId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: GOUserGroup.GOUserId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GOUserGroup.GOUserId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 109: Check for NOT NULL constraint for FK GOSecurity.GOUserGroup.GOGroupName and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[GOSecurity].[GOUserGroup]') and name = 'GOGroupName') = 1
BEGIN
	IF NOT EXISTS (SELECT GOGroupName FROM [GOSecurity].[GOUserGroup] WHERE GOGroupName is NULL)
	BEGIN	
		ALTER TABLE [GOSecurity].[GOUserGroup] 
			ALTER COLUMN [GOGroupName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
		PRINT 'INFO: GOUserGroup.GOGroupName has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GOUserGroup.GOGroupName because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 110: Check for NOT NULL constraint for FK dbo.CustomerPreOperationalChecklistTemplate.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[CustomerPreOperationalChecklistTemplate]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[CustomerPreOperationalChecklistTemplate] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[CustomerPreOperationalChecklistTemplate] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: CustomerPreOperationalChecklistTemplate.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column CustomerPreOperationalChecklistTemplate.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 111: Check for NOT NULL constraint for FK dbo.BroadcastMessageHistory.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[BroadcastMessageHistory]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[BroadcastMessageHistory] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[BroadcastMessageHistory] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: BroadcastMessageHistory.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column BroadcastMessageHistory.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 112: Check for NOT NULL constraint for FK dbo.GoUserToCustomer.GOUserId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[GoUserToCustomer]') and name = 'GOUserId') = 1
BEGIN
	IF NOT EXISTS (SELECT GOUserId FROM [dbo].[GoUserToCustomer] WHERE GOUserId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[GoUserToCustomer] 
			ALTER COLUMN [GOUserId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: GoUserToCustomer.GOUserId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GoUserToCustomer.GOUserId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 113: Check for NOT NULL constraint for FK dbo.GoUserToCustomer.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[GoUserToCustomer]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[GoUserToCustomer] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[GoUserToCustomer] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: GoUserToCustomer.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column GoUserToCustomer.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 114: Check for NOT NULL constraint for FK dbo.Person.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Person]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[Person] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Person] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Person.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Person.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 115: Check for NOT NULL constraint for FK dbo.Person.SiteId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Person]') and name = 'SiteId') = 1
BEGIN
	IF NOT EXISTS (SELECT SiteId FROM [dbo].[Person] WHERE SiteId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Person] 
			ALTER COLUMN [SiteId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Person.SiteId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Person.SiteId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 116: Check for NOT NULL constraint for FK dbo.Person.DepartmentId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Person]') and name = 'DepartmentId') = 1
BEGIN
	IF NOT EXISTS (SELECT DepartmentId FROM [dbo].[Person] WHERE DepartmentId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Person] 
			ALTER COLUMN [DepartmentId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Person.DepartmentId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Person.DepartmentId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 117: Check for NOT NULL constraint for FK dbo.NetworkSettings.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[NetworkSettings]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[NetworkSettings] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[NetworkSettings] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: NetworkSettings.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column NetworkSettings.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 118: Check for NOT NULL constraint for FK dbo.Department.CustomerId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Department]') and name = 'CustomerId') = 1
BEGIN
	IF NOT EXISTS (SELECT CustomerId FROM [dbo].[Department] WHERE CustomerId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Department] 
			ALTER COLUMN [CustomerId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Department.CustomerId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Department.CustomerId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 119: Check for NOT NULL constraint for FK dbo.Department.SiteId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[Department]') and name = 'SiteId') = 1
BEGIN
	IF NOT EXISTS (SELECT SiteId FROM [dbo].[Department] WHERE SiteId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[Department] 
			ALTER COLUMN [SiteId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: Department.SiteId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column Department.SiteId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 120: Check for NOT NULL constraint for FK dbo.PerVehicleMasterCardAccess.CardId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PerVehicleMasterCardAccess]') and name = 'CardId') = 1
BEGIN
	IF NOT EXISTS (SELECT CardId FROM [dbo].[PerVehicleMasterCardAccess] WHERE CardId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PerVehicleMasterCardAccess] 
			ALTER COLUMN [CardId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PerVehicleMasterCardAccess.CardId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PerVehicleMasterCardAccess.CardId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 121: Check for NOT NULL constraint for FK dbo.PerVehicleMasterCardAccess.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[PerVehicleMasterCardAccess]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[PerVehicleMasterCardAccess] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[PerVehicleMasterCardAccess] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: PerVehicleMasterCardAccess.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column PerVehicleMasterCardAccess.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 122: Check for NOT NULL constraint for FK dbo.OnDemandSettings.VehicleId and try to apply if not already done so
IF (SELECT is_nullable FROM sys.columns WHERE object_id = object_id('[dbo].[OnDemandSettings]') and name = 'VehicleId') = 1
BEGIN
	IF NOT EXISTS (SELECT VehicleId FROM [dbo].[OnDemandSettings] WHERE VehicleId is NULL)
	BEGIN	
		ALTER TABLE [dbo].[OnDemandSettings] 
			ALTER COLUMN [VehicleId] [uniqueidentifier] NOT NULL 
		PRINT 'INFO: OnDemandSettings.VehicleId has been constrained successfully (Mandatory, NOT NULL)'
	END
	ELSE
		PRINT 'WARNING: Unable to apply NOT NULL constraint to column OnDemandSettings.VehicleId because there is at least one existing NULL value in the database. Column constraint will remain NULL (optional).'
END
GO
-- step 123: Check for Unique Constraint U_OnDemandSettingsVehicle on [dbo].[OnDemandSettings] and try to apply if not already done so
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'U_OnDemandSettingsVehicle' AND object_id = OBJECT_ID('[dbo].[OnDemandSettings]'))
BEGIN
	IF (SELECT COUNT (*) FROM [dbo].[OnDemandSettings] WHERE [VehicleId] IS NULL) <= 1 
	BEGIN
		CREATE UNIQUE NONCLUSTERED INDEX [U_OnDemandSettingsVehicle]
		ON [dbo].[OnDemandSettings] 
			([VehicleId]) 
		PRINT 'INFO: Unique Constraint U_OnDemandSettingsVehicle has been applied successfully'
	END
	ELSE
	BEGIN
		PRINT 'WARNING: Failed to apply Unique Constraint on entity table OnDemandSettings - there is more than one existing NULL in mandatory column(s): [VehicleId]'
	END
END
GO
