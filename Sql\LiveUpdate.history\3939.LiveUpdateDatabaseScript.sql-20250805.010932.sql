﻿-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- BEGIN LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
SET NOCOUNT ON
SET NOEXEC OFF
SET ARITHABORT ON
SET XACT_ABORT ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO
BEGIN TRAN
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- TRANSFER SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- RENAME TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (soft)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN RENAMEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN ADDs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN MODIFYs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 1: Modify column dbo.SlamcoreAPIKey.APIKeyDisplay 
-- Field 'APIKeyDisplay' was previously mandatory, now optional, so drop default constraint, if it exists
IF (OBJECT_ID(N'[dbo].[DF_field_id_220d1bdb-8ce8-4829-805b-9c5a4a052fb3]', 'D') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAPIKey] DROP CONSTRAINT [DF_field_id_220d1bdb-8ce8-4829-805b-9c5a4a052fb3]
END
GO
ALTER TABLE [dbo].[SlamcoreAPIKey] 
	ALTER COLUMN [APIKeyDisplay] [nvarchar] (1000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 1, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 1' SET NOEXEC ON END
GO
-- step 2: Modify column dbo.SlamcoreAwareAuthenticationDetails.PasswordDisplay 
-- Field 'PasswordDisplay' was previously mandatory, now optional, so drop default constraint, if it exists
IF (OBJECT_ID(N'[dbo].[DF_field_id_4b45d493-081e-42cf-a65f-b8c6f464bce9]', 'D') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] DROP CONSTRAINT [DF_field_id_4b45d493-081e-42cf-a65f-b8c6f464bce9]
END
GO
ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] 
	ALTER COLUMN [PasswordDisplay] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 2, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 2' SET NOEXEC ON END
GO
-- step 3: Modify column dbo.SlamcoreAwareAuthenticationDetails.UsernameDisplay 
-- Field 'UsernameDisplay' was previously mandatory, now optional, so drop default constraint, if it exists
IF (OBJECT_ID(N'[dbo].[DF_field_id_1c77e95e-4916-4f4c-96ed-30d25c247146]', 'D') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] DROP CONSTRAINT [DF_field_id_1c77e95e-4916-4f4c-96ed-30d25c247146]
END
GO
ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] 
	ALTER COLUMN [UsernameDisplay] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 3, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 3' SET NOEXEC ON END
GO
-- step 4: Modify column dbo.SlamcoreAwareAuthenticationDetails.Username 
-- Field 'Username' was previously mandatory, now optional, so drop default constraint, if it exists
IF (OBJECT_ID(N'[dbo].[DF_field_id_8c252d4f-3991-4098-9db7-b5e997c598c8]', 'D') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] DROP CONSTRAINT [DF_field_id_8c252d4f-3991-4098-9db7-b5e997c598c8]
END
GO
ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] 
	ALTER COLUMN [Username] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 4, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 4' SET NOEXEC ON END
GO
-- step 5: Modify column dbo.SlamcoreAwareAuthenticationDetails.Password 
-- Field 'Password' was previously mandatory, now optional, so drop default constraint, if it exists
IF (OBJECT_ID(N'[dbo].[DF_field_id_cef16d2b-a838-4b3c-9083-6f2496e9663b]', 'D') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] DROP CONSTRAINT [DF_field_id_cef16d2b-a838-4b3c-9083-6f2496e9663b]
END
GO
ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] 
	ALTER COLUMN [Password] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 5, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 5' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (hard)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MODEL TO DATABASE SYNCHRONISATION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
UPDATE [GO.LiveUpdate].[ModelSync] SET [ModelRevisionId] = 3939, [When] = GETUTCDATE() WHERE Id = 'AF3DF4FF-A05A-4969-9796-FAC22A6ED2AF'
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COMMIT LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
IF @@TRANCOUNT > 0 
BEGIN 
	COMMIT TRAN PRINT 'Synchronization completed successfully.' 
END
GO
SET NOEXEC OFF
GO
