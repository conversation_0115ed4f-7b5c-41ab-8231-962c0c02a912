﻿-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- BEGIN LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
SET NOCOUNT ON
SET NOEXEC OFF
SET ARITHABORT ON
SET XACT_ABORT ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO
BEGIN TRAN
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- TRANSFER SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 1: Drop Unique Constraints U_SlamcoreAccountAuthenticationDetailsCustomer 
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'U_SlamcoreAccountAuthenticationDetailsCustomer' AND object_id = OBJECT_ID('[dbo].[SlamcoreAccountAuthenticationDetails]'))
BEGIN
DROP INDEX U_SlamcoreAccountAuthenticationDetailsCustomer 
	ON [dbo].[SlamcoreAccountAuthenticationDetails]
END
GO
IF (OBJECT_ID(N'[dbo].[U_SlamcoreAccountAuthenticationDetailsCustomer]', 'UQ') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAccountAuthenticationDetails] DROP CONSTRAINT [U_SlamcoreAccountAuthenticationDetailsCustomer]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 1, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 1' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 2: Drop Foreign Key constraint dbo.SlamcoreAccountAuthenticationDetails 
IF (OBJECT_ID(N'[dbo].[FK_SlamcoreAccountAuthenticationDetails_Customer_6ccba92a-3ac8-4036-b3d8-6ce6ee63939a]', 'F') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAccountAuthenticationDetails] DROP CONSTRAINT [FK_SlamcoreAccountAuthenticationDetails_Customer_6ccba92a-3ac8-4036-b3d8-6ce6ee63939a]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 2, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 2' SET NOEXEC ON END
GO
-- step 3: Drop Foreign Key constraint dbo.SlamcoreDevice 
IF (OBJECT_ID(N'[dbo].[FK_SlamcoreDevice_SlamcoreAuthenticationDetails_7b8d8d19-9ca4-42d7-9df8-2339518bf0c3]', 'F') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreDevice] DROP CONSTRAINT [FK_SlamcoreDevice_SlamcoreAuthenticationDetails_7b8d8d19-9ca4-42d7-9df8-2339518bf0c3]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 3, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 3' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 4: Drop Primary Key constraint dbo.SlamcoreAccountAuthenticationDetails 
IF (OBJECT_ID(N'[dbo].[PK_SlamcoreAccountAuthenticationDetails]', 'PK') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAccountAuthenticationDetails] DROP CONSTRAINT [PK_SlamcoreAccountAuthenticationDetails]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 4, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 4' SET NOEXEC ON END
GO
-- step 5: Drop Primary Key constraint dbo.SlamcoreAuthenticationDetails 
IF (OBJECT_ID(N'[dbo].[PK_SlamcoreAuthenticationDetails]', 'PK') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAuthenticationDetails] DROP CONSTRAINT [PK_SlamcoreAuthenticationDetails]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 5, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 5' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- RENAME TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 6: Rename table dbo.SlamcoreAccountAuthenticationDetails 
EXEC sp_rename '[dbo].[SlamcoreAccountAuthenticationDetails]', 'SlamcoreAwareAuthenticationDetails', 'OBJECT'
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 6, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 6' SET NOEXEC ON END
GO
-- step 7: Rename table dbo.SlamcoreAuthenticationDetails 
EXEC sp_rename '[dbo].[SlamcoreAuthenticationDetails]', 'SlamcoreAPIKey', 'OBJECT'
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 7, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 7' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (soft)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 8: Soft drop (Rename out of the way) column dbo.SlamcoreAwareAuthenticationDetails.CustomerId 
EXEC sp_rename '[dbo].[SlamcoreAwareAuthenticationDetails].[CustomerId]', 'CustomerId_$@GO.dropped._$@GO', 'COLUMN'
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 8, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 8' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN RENAMEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN ADDs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN MODIFYs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (hard)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 9: Drop column dbo.SlamcoreAwareAuthenticationDetails.CustomerId_$@GO.dropped._$@GO 
-- first drop any default value constraint
IF (OBJECT_ID(N'[dbo].[DF_field_id_642eb097-833b-4748-90da-09eb60e15107]', 'D') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] DROP CONSTRAINT [DF_field_id_642eb097-833b-4748-90da-09eb60e15107]
END
GO
-- drop the column
ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] DROP COLUMN [CustomerId_$@GO.dropped._$@GO]
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 9, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 9' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 10: Add Primary Key Constraint dbo.SlamcoreAwareAuthenticationDetails 
ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] WITH NOCHECK 
	ADD CONSTRAINT [PK_SlamcoreAwareAuthenticationDetails] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 10, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 10' SET NOEXEC ON END
GO
-- step 11: Add Primary Key Constraint dbo.SlamcoreAPIKey 
ALTER TABLE [dbo].[SlamcoreAPIKey] WITH NOCHECK 
	ADD CONSTRAINT [PK_SlamcoreAPIKey] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 11, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 11' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 12: Add Foreign Key Constraint dbo.SlamcoreDevice to dbo.SlamcoreAPIKey 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD CONSTRAINT [FK_SlamcoreDevice_SlamcoreAPIKey_7b8d8d19-9ca4-42d7-9df8-2339518bf0c3] FOREIGN KEY
	(
		[SlamcoreAuthenticationDetailsId] 
	)
	REFERENCES [dbo].[SlamcoreAPIKey]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 12, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 12' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MODEL TO DATABASE SYNCHRONISATION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
UPDATE [GO.LiveUpdate].[ModelSync] SET [ModelRevisionId] = 3932, [When] = GETUTCDATE() WHERE Id = 'AF3DF4FF-A05A-4969-9796-FAC22A6ED2AF'
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COMMIT LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
IF @@TRANCOUNT > 0 
BEGIN 
	COMMIT TRAN PRINT 'Synchronization completed successfully.' 
END
GO
SET NOEXEC OFF
GO
