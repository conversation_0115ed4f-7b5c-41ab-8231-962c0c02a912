﻿-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- BEGIN LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
SET NOCOUNT ON
SET NOEXEC OFF
SET ARITHABORT ON
SET XACT_ABORT ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO
BEGIN TRAN
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- TRANSFER SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- RENAME TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (soft)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN RENAMEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN ADDs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN MODIFYs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (hard)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MODEL TO DATABASE SYNCHRONISATION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
UPDATE [GO.LiveUpdate].[ModelSync] SET [ModelRevisionId] = 3919, [When] = GETUTCDATE() WHERE Id = 'AF3DF4FF-A05A-4969-9796-FAC22A6ED2AF'
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COMMIT LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
IF @@TRANCOUNT > 0 
BEGIN 
	COMMIT TRAN PRINT 'Synchronization completed successfully.' 
END
GO
SET NOEXEC OFF
GO
