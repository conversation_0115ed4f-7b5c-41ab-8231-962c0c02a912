sequenceDiagram
    participant Client as 🌐 Web Client
    participant Subdomain as 🔗 Subdomain Middleware
    participant Auth as 🔐 Access Control Middleware
    participant API as 🚪 API Controller
    participant Business as ⚙️ Business Component
    participant Data as 💾 Data Provider
    participant NHibernate as 🔧 NHibernate ORM
    participant SQL as 🗄️ SQL Server
    participant Redis as ⚡ Redis Cache
    participant Azure as ☁️ Azure Services
    participant Error as ❌ Error Handler

    %% Request Flow - Client to API
    Client->>Subdomain: HTTP Request (GET /api/vehicleapi/unlock)
    Note over Client,Subdomain: Extract subdomain, validate dealer
    
    Subdomain->>Auth: Forward Request
    Note over Auth: Validate Bearer Token, Check Permissions
    
    alt Authentication Failed
        Auth->>Error: Token Invalid/Expired
        Error->>Client: 401 Unauthorized
    else Authentication Success
        Auth->>API: Authenticated Request
        
        %% API Controller Processing
        API->>API: Validate CSRF Token
        API->>API: Deserialize Request Data
        API->>API: Log Request (Extensions.OnRequestAction)
        
        %% Business Layer Processing
        API->>Business: Call VehicleAPI.UnlockAsync(vehicleId)
        Note over Business: Business Logic Validation, Rules Processing
        
        %% Data Access Layer
        Business->>Data: Get Vehicle Data
        Data->>NHibernate: Create NHibernate Query
        
        %% Database Interaction
        NHibernate->>SQL: Execute SQL Query
        Note over NHibernate,SQL: SELECT * FROM Vehicle WHERE Id = @vehicleId
        
        %% Cache Check
        alt Data in Cache
            Redis->>Data: Return Cached Data
        else Data Not in Cache
            SQL->>NHibernate: Return Vehicle Data
            NHibernate->>Data: Map to Data Object
            Data->>Redis: Cache Vehicle Data
        end
        
        %% Business Logic Processing
        Data->>Business: Return VehicleDataObject
        Business->>Business: Apply Business Rules
        Business->>Business: Validate Unlock Permissions
        Business->>Business: Update Vehicle Status
        
        %% Update Database
        Business->>Data: Save Updated Vehicle
        Data->>NHibernate: Update Query
        NHibernate->>SQL: UPDATE Vehicle SET Status = 'Unlocked'
        SQL->>NHibernate: Confirm Update
        NHibernate->>Data: Return Success
        
        %% External Service Integration
        alt IoT Integration Required
            Business->>Azure: Send Unlock Command to IoT Hub
            Azure->>Azure: Process Device Command
            Azure->>Business: Confirm Device Response
        end
        
        %% Response Flow
        Data->>Business: Return Updated Vehicle
        Business->>API: Return ComponentResponse<VehicleDataObject>
        
        %% API Response Processing
        API->>API: Serialize Response Data
        API->>API: Log Response (Extensions.OnRequestAction)
        API->>Auth: Return ActionResult
        
        %% Response to Client
        Auth->>Subdomain: Forward Response
        Subdomain->>Client: HTTP 200 OK + Vehicle Data
        
    end
    
    %% Error Handling Paths
    rect rgb(255, 235, 238)
        Note over Error: Error Handling Scenarios
        
        alt Database Error
            SQL->>NHibernate: Database Exception
            NHibernate->>Data: Throw DataAccessException
            Data->>Business: Propagate Exception
            Business->>API: Return Error Response
            API->>Client: 500 Internal Server Error
        end
        
        alt Business Rule Violation
            Business->>Business: Validation Failed
            Business->>API: Return Business Exception
            API->>Client: 400 Bad Request
        end
        
        alt External Service Error
            Azure->>Business: IoT Service Unavailable
            Business->>API: Return Service Exception
            API->>Client: 503 Service Unavailable
        end
    end
    
    %% Caching Strategy
    rect rgb(232, 245, 233)
        Note over Redis: Caching Strategy
        
        Client->>Redis: Check Cache First
        alt Cache Hit
            Redis->>Client: Return Cached Response
        else Cache Miss
            Redis->>API: Cache Miss, Proceed to Database
            API->>SQL: Query Database
            SQL->>API: Return Data
            API->>Redis: Store in Cache
            API->>Client: Return Fresh Data
        end
    end
    
    %% Security Flow
    rect rgb(255, 243, 224)
        Note over Auth: Security Flow Details
        
        Auth->>Auth: Extract Bearer Token
        Auth->>Auth: Validate Token Signature
        Auth->>Auth: Check Token Expiration
        Auth->>Auth: Verify User Permissions
        Auth->>Auth: Set Thread Context
        Auth->>Auth: Apply Security Filters
    end
    
    %% Data Flow Details
    rect rgb(237, 231, 246)
        Note over Data: Data Access Flow
        
        Data->>Data: Apply Security Filters
        Data->>Data: Build Dynamic Queries
        Data->>Data: Handle Lazy Loading
        Data->>Data: Manage Transactions
        Data->>Data: Apply Auto-Include Rules
    end
    
    %% Performance Monitoring
    rect rgb(241, 248, 233)
        Note over API: Performance Monitoring
        
        API->>API: Start Request Timer
        API->>API: Log Performance Metrics
        API->>API: Track Database Queries
        API->>API: Monitor Cache Hit Rate
        API->>API: Record Response Time
    end

    %% Legend
    Note over Client,Error: 🎯 XQ360 Fleet Management System - Complete API Request Flow
    Note over Client,Error: 
    Note over Client,Error: Architecture Layers:
    Note over Client,Error: 🌐 Web Application Layer (Client, Subdomain Middleware)
    Note over Client,Error: 🔐 Service Layer (Auth, API Controllers)
    Note over Client,Error: ⚙️ Business Layer (Business Components)
    Note over Client,Error: 💾 Data Layer (Data Providers, NHibernate ORM)
    Note over Client,Error: 🗄️ Storage Layer (SQL Server, Redis Cache)
    Note over Client,Error: ☁️ Integration Layer (Azure Services) 