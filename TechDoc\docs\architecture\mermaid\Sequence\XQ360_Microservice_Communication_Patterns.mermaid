graph TB
    %% Styling Definitions
    classDef webLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef apiLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef businessLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef azureService fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef queue fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef cache fill:#fff8e1,stroke:#ffa000,stroke-width:2px

    %% Web Application Layer
    subgraph WebLayer ["🌐 Web Application Layer"]
        WebApp["🌐 Web Application<br/>JavaScript, HTML5, CSS3<br/>Responsive UI Components"]
        SubdomainMiddleware["🔗 Subdomain Middleware<br/>Multi-tenant Routing<br/>Dealer Isolation"]
    end

    %% API Gateway & Service Layer
    subgraph APILayer ["🚪 API Gateway & Service Layer"]
        APIGateway["🚪 API Gateway<br/>Centralized Routing<br/>Rate Limiting & CORS"]
        AccessControlMiddleware["🔐 Access Control Middleware<br/>JWT Token Validation<br/>Role-based Authorization"]
        EntityControllers["📋 Entity Controllers<br/>207+ REST Endpoints<br/>CRUD Operations"]
        ComponentControllers["⚙️ Component Controllers<br/>85+ Business Logic APIs<br/>Complex Operations"]
    end

    %% Business Logic Layer
    subgraph BusinessLayer ["🏢 Business Logic Layer"]
        CoreServices["🏢 Core Services<br/>Fleet Management Logic<br/>Business Rules Engine"]
        VehicleAccessCreation["🚗 Vehicle Access Creation<br/>Access Provisioning<br/>Permission Management"]
        IoTHubManager["📡 IoT Hub Manager<br/>Device Communication<br/>Telemetry Processing"]
        WorkflowEngine["🔄 Workflow Engine<br/>Automated Processes<br/>Business Workflows"]
        NotificationService["📢 Notification Service<br/>Real-time Alerts<br/>Email & SMS"]
    end

    %% Queue Services
    subgraph QueueServices ["📬 Queue Services"]
        UserAccessQueueService["👤 User Access Queue<br/>Azure Service Bus<br/>Async User Updates"]
        VehicleAccessQueueService["🚛 Vehicle Access Queue<br/>Azure Service Bus<br/>Async Vehicle Sync"]
        VehicleSyncQueueService["🔄 Vehicle Sync Queue<br/>Azure Service Bus<br/>IoT Device Sync"]
    end

    %% Azure Functions
    subgraph AzureFunctions ["⚡ Azure Functions"]
        VehicleAccessProcessor["🚗 Vehicle Access Processor<br/>Process Vehicle Creation<br/>IoT Device Sync"]
        UserAccessProcessor["👤 User Access Processor<br/>Process User Updates<br/>Access Management"]
        HealthCheckFunction["💚 Health Check Function<br/>System Monitoring<br/>Connectivity Tests"]
    end

    %% Data Layer
    subgraph DataLayer ["💾 Data Layer"]
        NHibernate["🔧 NHibernate ORM<br/>Data Access Layer<br/>Object-Relational Mapping"]
        SQLServer["🗄️ SQL Server Database<br/>Primary Data Store<br/>ACID Transactions"]
        Redis["⚡ Redis Cache<br/>High-performance Caching<br/>Session Storage"]
        FileStorage["📁 File Storage<br/>Document & Media<br/>Secure Storage"]
    end

    %% External Azure Services
    subgraph AzureServices ["☁️ Azure Services"]
        AzureServiceBus["📬 Azure Service Bus<br/>Message Broker<br/>Reliable Delivery"]
        AzureIoTHub["📡 Azure IoT Hub<br/>Device Management<br/>Telemetry Collection"]
        AzureBlobStorage["🗂️ Azure Blob Storage<br/>File Storage<br/>Media Assets"]
        ApplicationInsights["📊 Application Insights<br/>Monitoring & Analytics<br/>Performance Tracking"]
    end

    %% External Systems
    subgraph ExternalSystems ["🌍 External Systems"]
        ThirdPartyAPIs["🔌 Third-party APIs<br/>External Services<br/>Data Exchange"]
        WebhookEndpoints["🎣 Webhook Endpoints<br/>Event Notifications<br/>Real-time Updates"]
        IoTDevices["📱 IoT Devices<br/>Vehicle Telemetry<br/>Access Control"]
    end

    %% Communication Flow - Synchronous
    WebApp --> SubdomainMiddleware
    SubdomainMiddleware --> APIGateway
    APIGateway --> AccessControlMiddleware
    AccessControlMiddleware --> EntityControllers
    AccessControlMiddleware --> ComponentControllers

    EntityControllers --> CoreServices
    ComponentControllers --> VehicleAccessCreation
    ComponentControllers --> IoTHubManager
    ComponentControllers --> WorkflowEngine
    ComponentControllers --> NotificationService

    %% Communication Flow - Asynchronous (Queues)
    VehicleAccessCreation --> VehicleAccessQueueService
    VehicleAccessCreation --> UserAccessQueueService
    CoreServices --> VehicleSyncQueueService

    VehicleAccessQueueService --> AzureServiceBus
    UserAccessQueueService --> AzureServiceBus
    VehicleSyncQueueService --> AzureServiceBus

    AzureServiceBus --> VehicleAccessProcessor
    AzureServiceBus --> UserAccessProcessor

    VehicleAccessProcessor --> VehicleAccessCreation
    UserAccessProcessor --> VehicleAccessCreation

    %% Data Layer Communication
    CoreServices --> NHibernate
    VehicleAccessCreation --> NHibernate
    IoTHubManager --> NHibernate
    WorkflowEngine --> NHibernate
    NotificationService --> Redis

    NHibernate --> SQLServer
    NHibernate --> Redis

    %% Azure Services Communication
    IoTHubManager --> AzureIoTHub
    VehicleAccessProcessor --> AzureIoTHub
    UserAccessProcessor --> AzureIoTHub

    VehicleAccessCreation --> AzureBlobStorage
    NotificationService --> AzureBlobStorage

    %% External System Communication
    CoreServices --> ThirdPartyAPIs
    NotificationService --> WebhookEndpoints
    AzureIoTHub --> IoTDevices

    %% Health Monitoring
    HealthCheckFunction --> SQLServer
    HealthCheckFunction --> Redis
    HealthCheckFunction --> AzureServiceBus
    HealthCheckFunction --> AzureIoTHub

    %% Apply Styling
    class WebLayer,WebApp,SubdomainMiddleware webLayer
    class APILayer,APIGateway,AccessControlMiddleware,EntityControllers,ComponentControllers apiLayer
    class BusinessLayer,CoreServices,VehicleAccessCreation,IoTHubManager,WorkflowEngine,NotificationService businessLayer
    class DataLayer,NHibernate,SQLServer,FileStorage dataLayer
    class AzureServices,AzureServiceBus,AzureIoTHub,AzureBlobStorage,ApplicationInsights azureService
    class ExternalSystems,ThirdPartyAPIs,WebhookEndpoints,IoTDevices external
    class QueueServices,UserAccessQueueService,VehicleAccessQueueService,VehicleSyncQueueService queue
    class Redis cache
    class AzureFunctions,VehicleAccessProcessor,UserAccessProcessor,HealthCheckFunction azureService

    %% Title and Legend
    title["🎯 XQ360 Fleet Management System<br/>Microservice Communication Patterns"]

    %% Legend
    subgraph Legend ["Architecture Legend"]
        L1["🌐 Web Layer<br/>User Interface & Routing"]
        L2["🚪 API Layer<br/>Gateway & Controllers"]
        L3["🏢 Business Layer<br/>Core Services & Logic"]
        L4["💾 Data Layer<br/>Storage & ORM"]
        L5["☁️ Azure Services<br/>Cloud Infrastructure"]
        L6["🌍 External Systems<br/>Third-party Integration"]
        L7["📬 Queue Services<br/>Async Communication"]
        L8["⚡ Azure Functions<br/>Serverless Processing"]
    end

    %% Apply legend styling
    class L1 webLayer
    class L2 apiLayer
    class L3 businessLayer
    class L4 dataLayer
    class L5 azureService
    class L6 external
    class L7 queue
    class L8 azureService

    %% Communication Pattern Notes
    subgraph CommunicationPatterns ["🔄 Communication Patterns"]
        CP1["📡 Synchronous: HTTP/REST APIs<br/>Real-time request/response"]
        CP2["📬 Asynchronous: Azure Service Bus<br/>Reliable message queuing"]
        CP3["🗄️ Database: NHibernate ORM<br/>ACID transactions"]
        CP4["⚡ Caching: Redis<br/>High-performance data access"]
        CP5["📱 IoT: Azure IoT Hub<br/>Device telemetry & control"]
    end

    %% Technology Stack Notes
    subgraph TechnologyStack ["🛠️ Technology Stack"]
        TS1["⚡ .NET Core 6+<br/>ASP.NET Core Web APIs"]
        TS2["🔧 NHibernate 5.x<br/>Object-Relational Mapping"]
        TS3["🗄️ SQL Server 2019+<br/>Primary Database"]
        TS4["⚡ Redis 6.x<br/>Caching & Sessions"]
        TS5["📡 Azure IoT Hub<br/>Device Management"]
        TS6["📬 Azure Service Bus<br/>Message Broker"]
        TS7["⚡ Azure Functions<br/>Serverless Processing"]
        TS8["🌐 JavaScript/HTML5<br/>Frontend Framework"]
    end 