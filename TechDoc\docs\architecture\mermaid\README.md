# Mermaid Diagrams Guide

This directory contains Mermaid diagram files that document the FleetXQ system architecture, workflows, and data models.

## Available Diagrams

### System Architecture
- **xq360-architecture-diagram.mermaid** - High-level system architecture overview
- **XQ360_Fleet_Management_System_Architecture.mermaid** - Detailed fleet management system architecture

### State Diagrams
- **XQ360_Authentication_State_Diagram.mermaid** - Authentication flow states
- **XQ360_Module_Status_State_Diagram.mermaid** - Module status transitions
- **XQ360_Vehicle_Access_Provisioning_State_Diagram.mermaid** - Vehicle access provisioning workflow
- **XQ360_IoT_Device_Sync_State_Diagram.mermaid** - IoT device synchronization states
- **XQ360_Driver_Status_State_Diagram.mermaid** - Driver status management
- **XQ360_Vehicle_Lifecycle_State_Diagram.mermaid** - Vehicle lifecycle management
- **XQ360_UI_State_Diagram.mermaid** - User interface state management

### Sequence Diagrams
- **XQ360_Microservice_Sequence_Diagram.mermaid** - Microservice communication sequences
- **XQ360_Microservice_Communication_Patterns.mermaid** - Communication patterns between services
- **XQ360_Frontend_Component_Hierarchy_Sequence_Diagram.mermaid** - Frontend component interactions
- **XQ360_API_Request_Flow_Sequence_Diagram.mermaid** - API request processing flow

### Data Models
- **VehicleAccess_ERD.mermaid** - Vehicle Access Entity Relationship Diagram

## How to View Mermaid Diagrams

### Option 1: Using Cursor IDE (Recommended)

You can easily use this by installing the Cursor extension "Mermaid Chart". Then in Cursor:
1. Open any `.mermaid` file
2. Right-click on the file
3. Select "Preview Diagram"

This will render the Mermaid diagram directly in Cursor, making it easy to view and understand the system architecture.

### Option 2: Online Mermaid Live Editor

1. Go to [Mermaid Live Editor](https://mermaid.live/)
2. Copy the content of any `.mermaid` file
3. Paste it into the editor
4. The diagram will render automatically on the right side

### Option 3: GitHub/GitLab

If these files are viewed on GitHub or GitLab, Mermaid diagrams will render automatically when viewing `.mermaid` files.

### Option 4: VS Code Extension (MOST RECOMMENDED)

1. Install the "Mermaid Chart" extension in Cursor
2. Open any `.mermaid` file
3. Use the command palette (Ctrl+Shift+P) and run "Mermaid Preview: Open Preview" or right-click "Preview"

## Creating New Diagrams

When creating new Mermaid diagrams:

1. Use the `.mermaid` file extension
2. Follow the existing naming convention: `XQ360_[Description]_[Type].mermaid`
3. Include a brief description in the diagram comments
4. Keep diagrams focused on a single concept or workflow
5. Use consistent styling and formatting

## Mermaid Syntax Examples

### State Diagram
```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> Processing : Start
    Processing --> Completed : Success
    Processing --> Error : Failure
    Completed --> [*]
    Error --> [*]
```

### Sequence Diagram
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Service
    participant Database
    
    Client->>API: Request
    API->>Service: Process
    Service->>Database: Query
    Database-->>Service: Result
    Service-->>API: Response
    API-->>Client: Response
```

### Entity Relationship Diagram
```mermaid
erDiagram
    CUSTOMER ||--o{ ORDER : places
    ORDER ||--|{ ORDER_ITEM : contains
    PRODUCT ||--o{ ORDER_ITEM : includes
```

## Best Practices

1. **Keep diagrams simple** - Focus on one concept per diagram
2. **Use descriptive names** - Make node and relationship names clear
3. **Consistent styling** - Use the same colors and styles across related diagrams
4. **Document relationships** - Clearly show dependencies and data flow
5. **Update regularly** - Keep diagrams in sync with code changes

## Troubleshooting

- If diagrams don't render, check the Mermaid syntax for errors
- Ensure all nodes are properly connected
- Verify that the file extension is `.mermaid`
- Check that the Mermaid extension is properly installed in your IDE

## Contributing

When adding new diagrams or modifying existing ones:

1. Test the diagram syntax using the Mermaid Live Editor
2. Ensure the diagram accurately represents the current system
3. Update this README if adding new diagram categories
4. Follow the established naming conventions
5. Include appropriate comments in the diagram code
